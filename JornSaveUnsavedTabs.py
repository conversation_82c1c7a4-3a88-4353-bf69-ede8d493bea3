# =======================================================
# [2025.02.22 11:54]
# 'https://chatgpt.com/c/67b99d2e-5ee8-8008-ab22-f5ab24445ab3'
# 'https://www.perplexity.ai/search/your-goal-is-to-familiarize-yo-_77QazF0RSujzJqXzkoT8Q'



import os
import datetime
import hashlib
import textwrap
import sublime
import sublime_plugin
import re

# Map Sublime Text syntaxes to file extensions.
EXTENSION_MAPPING = {
    "Bash": ".bash", "Batch File": ".bat", "C": ".c", "C++": ".cpp", "Command Prompt": ".cmd",
    "CSS": ".css", "Dockerfile": ".dockerfile", "Go": ".go", "Haskell": ".hs", "HTML": ".html",
    "INI": ".ini", "Java": ".java", "JavaScript": ".js", "JSON": ".json", "Kotlin": ".kt",
    "Less": ".less", "Lua": ".lua", "Makefile": ".mk", "Markdown": ".md", "MaxScript": ".ms",
    "Nginx Config": ".conf", "NSS": ".nss", "Objective-C": ".m", "Perl": ".pl", "PHP": ".php",
    "Plain Text": ".txt", "PowerShell": ".ps1", "Python": ".py", "Ruby": ".rb", "Rust": ".rs",
    "Scala": ".scala", "SCSS": ".scss", "ShellScript": ".sh", "SQL": ".sql", "Swift": ".swift",
    "TypeScript": ".ts", "Visual Basic": ".vb", "XML": ".xml", "YAML": ".yaml",
}

class JornSaveAllUnsavedTabsCommand(sublime_plugin.WindowCommand):
    def run(self, target_directory=None, auto_open_target_directory=False, auto_close_tabs=False):
        target_directory = self.ensure_directory(target_directory)
        existing_files, existing_hashes = self.scan_existing_files(target_directory)

        # For each bulk-save call, we generate a new session index regardless of the minute.
        date_str, session_index_str, hour_str = self.get_session_prefix(target_directory)

        unsaved_tabs = [view for view in self.window.views() if not view.file_name()]
        for view in unsaved_tabs:
            self.process_tab(view, target_directory, existing_files, existing_hashes,
                             date_str, session_index_str, hour_str, auto_close_tabs)

        if auto_open_target_directory:
            self.window.run_command("open_dir", {"dir": target_directory})

    def ensure_directory(self, target_directory):
        if not target_directory:
            target_directory = self.default_backup_directory()
        os.makedirs(target_directory, exist_ok=True)
        return target_directory

    def default_backup_directory(self):
        project_folders = self.window.folders()
        base_dir = os.path.dirname(os.path.abspath(__file__))
        if project_folders:
            project_name = os.path.basename(project_folders[0])
            return os.path.join(base_dir, "unsaved_tabs_backup", project_name)
        return os.path.join(base_dir, "unsaved_tabs_backup")

    def scan_existing_files(self, target_directory):
        existing_files = {fname for _, _, files in os.walk(target_directory) for fname in files}
        existing_hashes = set()
        for fname in existing_files:
            file_path = os.path.join(target_directory, fname)
            if os.path.isfile(file_path):
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                    existing_hashes.add(hashlib.md5(content.encode()).hexdigest())
                except Exception:
                    pass
        return existing_files, existing_hashes

    def get_session_prefix(self, target_directory):
        """
        Returns:
          date_str (e.g. "2025.02.22"),
          session_index_str (e.g. "001"),
          hour_str (e.g. "20").

        This function increments the session index (the 001 number) for each bulk-save call.
        It scans the backup directory for files from today's date and picks the next available index.
        The hour_str is taken from the current hour.
        """
        now = datetime.datetime.now()
        date_str = now.strftime("%Y.%m.%d")  # e.g., "2025.02.22"
        hour_str = now.strftime("%H")         # e.g., "20"

        # Pattern to match filenames like:
        # "2025.02.22-dsk-001-a.kl20__len[...].ext"
        pattern = re.compile(r"^" + re.escape(date_str) + r"-dsk-(\d{3})-[a-z]\.kl\d{2}__")

        session_indexes = []
        for fname in os.listdir(target_directory):
            m = pattern.match(fname)
            if m:
                session_indexes.append(int(m.group(1)))
        new_idx = (max(session_indexes) + 1) if session_indexes else 1
        session_index_str = f"{new_idx:03d}"

        return date_str, session_index_str, hour_str

    def process_tab(self, view, target_directory, existing_files, existing_hashes,
                    date_str, session_index_str, hour_str, auto_close_tabs):
        content = view.substr(sublime.Region(0, view.size())).strip()
        if not content:
            view.set_scratch(True)
            view.close()
            return

        content_hash = hashlib.md5(content.encode()).hexdigest()
        if content_hash in existing_hashes:
            sublime.status_message("Duplicate content detected. Skipping tab.")
            return

        extension = self.determine_extension(view)
        filename = self.create_unique_filename(date_str, session_index_str, hour_str, extension, existing_files, content)
        self.save_tab_content(view, content, target_directory, filename, existing_hashes, auto_close_tabs)
        existing_files.add(filename)

    def determine_extension(self, view):
        syntax = (view.settings().get("syntax") or "Plain Text").lower()
        for key, ext in EXTENSION_MAPPING.items():
            if key.lower() in syntax.lower() and len(key.lower()) >= 3:
                return ext
        return ".txt"

    def create_unique_filename(self, date_str, session_index_str, hour_str, ext, existing_files, content):
        wrapped_lines = len(textwrap.fill(content, width=100).splitlines())
        base_prefix = f"{date_str}-dsk-{session_index_str}-"
        hour_part = f".kl{hour_str}__"

        # Determine the letter suffix by scanning existing files for this bulk-save session.
        used_letters = set()
        for fname in existing_files:
            if fname.startswith(base_prefix) and hour_part in fname:
                remainder = fname[len(base_prefix):]
                letter_candidate = remainder.split(".kl", 1)[0]
                if len(letter_candidate) == 1 and letter_candidate.isalpha():
                    used_letters.add(letter_candidate)

        next_letter = 'a'
        while next_letter in used_letters:
            next_letter = chr(ord(next_letter) + 1)

        filename = f"{date_str}-dsk-{session_index_str}-{next_letter}.kl{hour_str}__len[{wrapped_lines}]{ext}"
        return filename

    def save_tab_content(self, view, content, directory, filename, hashes, auto_close_tabs):
        path = os.path.join(directory, filename)
        try:
            with open(path, "w", encoding="utf-8") as file:
                file.write(content)
            hashes.add(hashlib.md5(content.encode()).hexdigest())
            view.set_scratch(True)
            if auto_close_tabs:
                view.close()
        except Exception as e:
            sublime.status_message(f"Error saving tab: {e}")
