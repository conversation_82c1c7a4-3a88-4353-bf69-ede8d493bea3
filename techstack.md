# Technology Stack

## Core Technologies
- **Python 3.8+**: Primary language for Sublime Text 4 plugin development
- **Sublime Text 4 API**: Plugin framework and API integration

## Plugin Architecture
- **sublime_plugin.WindowCommand**: Base class for window-level commands
- **sublime_plugin.TextCommand**: Base class for text/view-level commands

## Key Dependencies
- **os**: File system operations and path handling
- **time**: Time-based operations for tab modification checks
- **traceback**: Error logging and debugging
- **math**: Mathematical operations for layout calculations

## Configuration Files
- **`.sublime-commands`**: Command palette integration
- **`.sublime-menu`**: Context menu definitions
- **`.sublime-settings`**: Plugin configuration
- **`.sublime-keymap`**: Keyboard shortcuts

## Development Patterns
- **Error-first design**: Comprehensive exception handling
- **Graceful degradation**: Fallback behaviors for edge cases
- **Modular architecture**: Separated concerns per functionality
