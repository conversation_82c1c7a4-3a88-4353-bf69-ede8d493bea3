import os
import time
import traceback
import bisect
import sublime
import sublime_plugin

# --------------------------------------------------------
# 1) Constants
# --------------------------------------------------------
ALL_CATEGORIES = [
    "empty_and_deleted",
    "empty_and_unsaved",
    "deleted_not_empty",
    "empty_not_deleted",
    "unsaved_not_empty",
    "clean_and_external",
    "clean_and_project",
    "dirty_and_external",
    "dirty_and_project",
]

# --------------------------------------------------------
# 2) Utility Functions
# --------------------------------------------------------
def is_view_in_project(view):
    """
    Checks if a given view belongs to the current project.
    """
    file_path = view.file_name()
    if not file_path:
        return False
    window = view.window()
    if not window:
        return False
    project_folders = window.folders()
    return any(
        os.path.commonpath([file_path, folder]) == folder for folder in project_folders
    )


def compute_percentile(val, sorted_list):
    """
    Return the fraction (0.0 to 1.0) of items in 'sorted_list' that are <= val.
    Example: if val is the median, we get ~0.5.
    """
    idx = bisect.bisect_right(sorted_list, val)
    return idx / len(sorted_list) if sorted_list else 0.0


# --------------------------------------------------------
# 3) Core Tab Categorizer (Single-Pass Approach)
# --------------------------------------------------------
class JornTabCategorizer:
    """
    Collects data about all tabs once (maximum detail: "phase 3") in refresh_data().
    Then get_phase_data(1/2/3) to return subsets of that info.

    We also have fallback logic to ensure every tab has some "last_accessed" time.
    """

    def __init__(self, window):
        self.window = window
        self._categorized_data = None  # will hold category -> [details, details, ...]

    def refresh_data(self):
        """
        (Re)scan all tabs in the window, collecting maximum detail for each,
        including 'time_since_last_access'.
        """
        cat_map = {cat: [] for cat in ALL_CATEGORIES}

        # Single pass: gather "full detail" for each View
        for view in self.window.views():
            category = self._determine_category(view)
            if category not in cat_map:
                category = "empty_not_deleted"
            details = self._gather_full_details(view)
            cat_map[category].append(details)

        # Compute "compare to the rest" stats (time percentile)
        self._compute_time_percentiles(cat_map)
        self._categorized_data = cat_map

    def _compute_time_percentiles(self, cat_map):
        """
        Compute a percentile rank for each tab's 'time_since_last_access'
        so the user sees how it compares with others.
        """
        time_list = []
        for _, items in cat_map.items():
            for item in items:
                t = item["time_since_last_access"]
                if t is not None:
                    time_list.append(t)

        if not time_list:
            return

        time_list.sort()
        for _, items in cat_map.items():
            for item in items:
                t = item["time_since_last_access"]
                if t is not None:
                    item["time_percentile"] = compute_percentile(t, time_list)
                else:
                    item["time_percentile"] = None

    def get_phase_data(self, phase=1):
        """
        Returns the data in Phase 1, 2, or 3 format.
        If we haven't refreshed yet, do so automatically.
        """
        if self._categorized_data is None:
            self.refresh_data()

        result = {}
        for category_name, items in self._categorized_data.items():
            if phase == 1:
                # Phase 1 => just return the View objects
                result[category_name] = [item["view"] for item in items]
            elif phase == 2:
                # Phase 2 => (view, basic_info)
                phase2_list = []
                for item in items:
                    phase2_list.append((
                        item["view"],
                        {
                            "basename": item["basename"],
                            "size_chars": item["size_chars"],
                        }
                    ))
                result[category_name] = phase2_list
            else:
                # Phase 3 => (view, detailed_info)
                phase3_list = []
                for item in items:
                    phase3_list.append((
                        item["view"],
                        {
                            "basename": item["basename"],
                            "group": item["group"],
                            "index": item["index"],
                            "syntax": item["syntax"],
                            "line_count": item["line_count"],
                            "time_since_last_access": item["time_since_last_access"],
                            "time_percentile": item["time_percentile"],
                        }
                    ))
                result[category_name] = phase3_list

        return result

    # Provide old "phase" method names in case your code references them:
    def categorize_tabs_phase1(self):
        return self.get_phase_data(1)

    def categorize_tabs_phase2(self):
        return self.get_phase_data(2)

    def categorize_tabs_phase3(self):
        return self.get_phase_data(3)

    def _determine_category(self, view):
        """
        Basic classification of the view:
         - unsaved vs. external vs. in-project
         - empty vs. nonempty
         - dirty vs. clean
        """
        content_stripped = view.substr(sublime.Region(0, view.size())).strip()
        content_length = len(content_stripped)
        file_name = view.file_name()

        if not file_name:
            if content_length <= 1:
                return "empty_and_unsaved"
            return "unsaved_not_empty"
        else:
            if not os.path.exists(file_name):
                if content_length <= 1:
                    return "empty_and_deleted"
                return "deleted_not_empty"
            else:
                if is_view_in_project(view):
                    return "dirty_and_project" if view.is_dirty() else "clean_and_project"
                else:
                    return "dirty_and_external" if view.is_dirty() else "clean_and_external"

    def _gather_full_details(self, view):
        """
        Collect maximum info in a single pass (like Phase 3).
        Also ensure every tab has a last_accessed time, setting one if needed.
        """
        data = {
            "view": view,
            "basename": "Untitled",
            "group": 0,
            "index": 0,
            "syntax": "Plain text",
            "line_count": 0,
            "size_chars": view.size(),
            "time_since_last_access": None,
            "time_percentile": None,
        }
        try:
            window_instance = view.window()
            if window_instance:
                group_id, index_id = window_instance.get_view_index(view)
                data["group"] = group_id
                data["index"] = index_id

            file_name = view.file_name()
            if file_name:
                data["basename"] = os.path.basename(file_name)
            else:
                data["basename"] = f"Untitled ({view.size()} chars)"

            syntax_path = view.settings().get("syntax")
            if syntax_path:
                syntax_file = os.path.basename(syntax_path)
                syntax_name, _ = os.path.splitext(syntax_file)
                data["syntax"] = syntax_name

            data["line_count"] = view.rowcol(view.size())[0] + 1

            # Fallback if the event listener never set 'last_accessed'
            last_accessed = getattr(view, "last_accessed", None)
            if last_accessed is None:
                # We'll set it to "now" so that no tab is left N/A
                last_accessed = time.time()
                view.last_accessed = last_accessed

            data["time_since_last_access"] = time.time() - last_accessed

        except Exception as e:
            print(f"[gather_full_details] Error: {e}")
            print(traceback.format_exc())

        return data


# --------------------------------------------------------
# 4) Sublime Text Commands: Phase 1, 2, 3
# --------------------------------------------------------
class CategorizeTabsPhase1Command(sublime_plugin.WindowCommand):
    def run(self, categorize_and_print=False):
        if categorize_and_print:
            try:
                cat = JornTabCategorizer(self.window)
                categorized_views = cat.categorize_tabs_phase1()
                self._display_results_phase1(categorized_views)
            except Exception as e:
                print(f"[Phase1] Error: {e}")
                print(traceback.format_exc())
        else:
            print("[Phase1] Use 'categorize_and_print=True' to output results.")

    def _display_results_phase1(self, categorized_views):
        output_view = self.window.create_output_panel("jorn_phase1")
        output_view.set_read_only(False)
        output_view.run_command("erase_view_contents")

        panel_content = ""
        for category_name in ALL_CATEGORIES:
            items = categorized_views[category_name]
            panel_content += f"## {category_name} ({len(items)})\n"
            if items:
                for view in items:
                    file_name = view.file_name() or f"Untitled ({view.size()} chars)"
                    base_name = os.path.basename(file_name) if view.file_name() else file_name
                    panel_content += f" - {base_name}\n"
            else:
                panel_content += " - None\n"
            panel_content += "\n"

        output_view.run_command("insert_content", {"content": panel_content})
        output_view.set_read_only(True)
        self.window.run_command("show_panel", {"panel": "output.jorn_phase1"})


class CategorizeTabsPhase2Command(sublime_plugin.WindowCommand):
    def run(self, categorize_and_print=False):
        if categorize_and_print:
            try:
                cat = JornTabCategorizer(self.window)
                categorized_views = cat.categorize_tabs_phase2()
                self._display_results_phase2(categorized_views)
            except Exception as e:
                print(f"[Phase2] Error: {e}")
                print(traceback.format_exc())
        else:
            print("[Phase2] Use 'categorize_and_print=True' to output results.")

    def _display_results_phase2(self, categorized_views):
        output_view = self.window.create_output_panel("jorn_phase2")
        output_view.set_read_only(False)
        output_view.run_command("erase_view_contents")

        panel_content = ""
        for category_name in ALL_CATEGORIES:
            items = categorized_views[category_name]
            panel_content += f"## {category_name} ({len(items)})\n"
            if items:
                for view, info in items:
                    panel_content += f" - {info['basename']} (Size: {info['size_chars']} chars)\n"
            else:
                panel_content += " - None\n"
            panel_content += "\n"

        output_view.run_command("insert_content", {"content": panel_content})
        output_view.set_read_only(True)
        self.window.run_command("show_panel", {"panel": "output.jorn_phase2"})


class CategorizeTabsPhase3Command(sublime_plugin.WindowCommand):
    def run(self, categorize_and_print=False):
        if categorize_and_print:
            try:
                cat = JornTabCategorizer(self.window)
                categorized_views = cat.categorize_tabs_phase3()
                self._display_results_phase3(categorized_views)
            except Exception as e:
                print(f"[Phase3] Error: {e}")
                print(traceback.format_exc())
        else:
            print("[Phase3] Use 'categorize_and_print=True' to output results.")

    def _display_results_phase3(self, categorized_views):
        output_view = self.window.create_output_panel("jorn_phase3")
        output_view.set_read_only(False)
        output_view.run_command("erase_view_contents")

        panel_content = ""
        for category_name in ALL_CATEGORIES:
            items = categorized_views[category_name]
            panel_content += f"## {category_name} ({len(items)})\n"
            if items:
                for view, details in items:
                    time_str = "N/A"
                    pct_str = "N/A"
                    if details["time_since_last_access"] is not None:
                        time_str = f"{details['time_since_last_access']:.1f}s"
                    if details["time_percentile"] is not None:
                        pct_str = f"{details['time_percentile'] * 100:.0f}%"

                    panel_content += (
                        f" - {details['basename']} "
                        f"(Group: {details['group']}, Index: {details['index']}, "
                        f"Syntax: {details['syntax']}, Lines: {details['line_count']})\n"
                        f"   Last Access: {time_str} ago (Percentile: {pct_str})\n"
                    )
            else:
                panel_content += " - None\n"
            panel_content += "\n"

        output_view.run_command("insert_content", {"content": panel_content})
        output_view.set_read_only(True)
        self.window.run_command("show_panel", {"panel": "output.jorn_phase3"})


# --------------------------------------------------------
# 5) Helper Commands
# --------------------------------------------------------
class EraseViewContentsCommand(sublime_plugin.TextCommand):
    def run(self, edit):
        self.view.erase(edit, sublime.Region(0, self.view.size()))


class InsertContentCommand(sublime_plugin.TextCommand):
    def run(self, edit, content):
        self.view.insert(edit, self.view.size(), content)


# --------------------------------------------------------
# 6) Event Listener
# --------------------------------------------------------
class TabActivityListener(sublime_plugin.EventListener):
    """
    Ensures every tab has a "last_accessed" time.
    We use async hooks so we definitely catch the view states in Sublime Text 4+.

    This means:
      - on_new_async() => new tab is assigned last_accessed
      - on_load_async() => existing file loaded gets last_accessed
      - on_activated_async() => whenever you switch to a tab
      - on_modified_async() => whenever you change a tab's contents
    """

    def on_new_async(self, view):
        self._update_last_accessed(view)

    def on_load_async(self, view):
        self._update_last_accessed(view)

    def on_activated_async(self, view):
        self._update_last_accessed(view)

    def on_modified_async(self, view):
        self._update_last_accessed(view)

    def _update_last_accessed(self, view):
        try:
            view.last_accessed = time.time()
        except Exception as e:
            print(f"[JornEventListener] Error updating last_accessed: {e}")
            print(traceback.format_exc())
