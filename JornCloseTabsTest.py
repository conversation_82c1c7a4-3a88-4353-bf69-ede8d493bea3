# Test script for Jorn<PERSON><PERSON><PERSON>abs enhanced error handling
import sublime
import sublime_plugin

class JornCloseTabsTestCommand(sublime_plugin.WindowCommand):
    """
    Test command to verify Jo<PERSON><PERSON>loseTabs handles various scenarios gracefully.
    """
    
    def run(self):
        """Run various test scenarios for JornCloseTabs."""
        print("\n" + "="*60)
        print("JornCloseTabs Enhanced Error Handling Test")
        print("="*60)
        
        # Test 1: Normal operation with valid arguments
        print("\nTest 1: Normal operation with valid arguments")
        try:
            self.window.run_command("jorn_close_tabs", {
                "tab_types": ["empty"],
                "confirmation_prompt": True,
                "exclude_active_tabs": False
            })
            print("✓ Test 1 passed: Normal operation successful")
        except Exception as e:
            print(f"✗ Test 1 failed: {e}")
        
        # Test 2: Operation with unexpected arguments (should be handled gracefully)
        print("\nTest 2: Operation with unexpected arguments")
        try:
            self.window.run_command("jorn_close_tabs", {
                "tab_types": ["empty"],
                "confirmation_prompt": True,
                "exclude_active_tabs": False,
                "unexpected_arg": "should_be_ignored",
                "another_unexpected": 123
            })
            print("✓ Test 2 passed: Unexpected arguments handled gracefully")
        except Exception as e:
            print(f"✗ Test 2 failed: {e}")
        
        # Test 3: Operation with missing arguments (should use defaults)
        print("\nTest 3: Operation with minimal arguments")
        try:
            self.window.run_command("jorn_close_tabs", {})
            print("✓ Test 3 passed: Missing arguments handled with defaults")
        except Exception as e:
            print(f"✗ Test 3 failed: {e}")
        
        # Test 4: Operation with invalid tab types (should be handled gracefully)
        print("\nTest 4: Operation with invalid tab types")
        try:
            self.window.run_command("jorn_close_tabs", {
                "tab_types": ["invalid_type", "another_invalid"],
                "confirmation_prompt": True
            })
            print("✓ Test 4 passed: Invalid tab types handled gracefully")
        except Exception as e:
            print(f"✗ Test 4 failed: {e}")
        
        # Test 5: Operation with None values (should be handled gracefully)
        print("\nTest 5: Operation with None values")
        try:
            self.window.run_command("jorn_close_tabs", {
                "tab_types": None,
                "tab_syntaxes": None,
                "hours": None
            })
            print("✓ Test 5 passed: None values handled gracefully")
        except Exception as e:
            print(f"✗ Test 5 failed: {e}")
        
        print("\n" + "="*60)
        print("JornCloseTabs Test Complete")
        print("="*60)
        
        # Show completion message
        sublime.message_dialog("JornCloseTabs test completed. Check the console for results.")
