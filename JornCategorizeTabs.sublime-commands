// [
//     {
//         "caption": "Categorize Tabs",
//         "command": "jorn_categorize_tabs",
//         "args":
//         {
//             "current_group_only": false,
//             "tab_types": ["all"],
//             // "tab_types": ["empty", "inside_backups", "inside_tmp", "deleted", "not_in_project"],
//             "tab_syntaxes": [],
//             "confirmation_prompt": true,
//         }
//     },
// ]


// [
//     {
//         "caption": "Jorn: Categorize and Close Tabs",
//         "command": "jorn_categorize_tabs",
//         "args": {
//             "tab_types": [
//                 "saved_not_dirty",
//                 "saved_and_dirty",
//                 "unsaved_and_empty",
//                 "unsaved_not_empty",
//                 "deleted_and_empty",
//                 "deleted_not_empty",
//                 "project_and_dirty",
//                 "project_not_dirty",
//                 "non_project_and_saved",
//                 "non_project_and_dirty",
//                 "empty",
//                 "not_empty",
//                 "binary",
//                 "text",
//                 "read_only",
//                 "writable",
//                 "language_python",
//                 "language_javascript",
//                 "recently_active",
//                 "has_errors",
//                 "preview"
//             ],
//             "tab_syntaxes": [],
//             "current_group_only": false,
//             "confirmation_prompt": true,
//             "hours": null,
//             "categorize_and_print": false
//         }
//     },
//     {
//         "caption": "Jorn: Categorize and Print Tabs",
//         "command": "jorn_print_categorized_tabs",
//         "args": {
//             "current_group_only": false
//         }
//     }
// ]


[
    {
        "caption": "Jorn: Categorize and Print Tabs",
        // "command": "jorn_categorize_tabs_phase3",
        "command": "categorize_tabs_phase3",
        "args": {
            "categorize_and_print": true
        }
    }
]


// sublime.active_window().run_command("jorn_categorize_tabs_phase1", {"categorize_and_print": True})
