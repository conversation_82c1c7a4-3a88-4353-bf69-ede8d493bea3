
# [Close Tabs]
import os
import sublime
import sublime_plugin
import time
import math
import traceback

class JornCloseTabsCommand(sublime_plugin.WindowCommand):
    """
    Enhanced JornCloseTabs command with comprehensive error handling.

    This command safely closes tabs based on various criteria while gracefully
    handling any unexpected arguments or runtime errors.
    """

    # Plugin version for debugging
    VERSION = "2.0.0-enhanced"

    valid_tab_types = [
        "all", "empty", "saved", "deleted", "notsaved",
        "modified", "not_in_project", "inside_backups",
        "inside_tmp", "same_extension", "not_modified_in_hours"
    ]
    last_args = {}

    def __init__(self, window):
        """Initialize the command with enhanced error handling."""
        try:
            super().__init__(window)
            print(f"JornCloseTabs v{self.VERSION} initialized")
        except Exception as e:
            print(f"JornCloseTabs: Error during initialization: {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            # Continue anyway - don't let initialization errors stop the plugin

    def run(self, **kwargs):
        """
        Main entry point for the command. Uses **kwargs to handle any arguments gracefully,
        preventing TypeError from unexpected keyword arguments.
        """
        try:
            # Extract known parameters with defaults, ignoring unknown ones
            tab_types = kwargs.get('tab_types', [])
            tab_syntaxes = kwargs.get('tab_syntaxes', [])
            current_group_only = kwargs.get('current_group_only', True)
            confirmation_prompt = kwargs.get('confirmation_prompt', True)
            hours = kwargs.get('hours', None)
            exclude_active_tabs = kwargs.get('exclude_active_tabs', False)

            # Log any unexpected arguments for debugging
            expected_args = {'tab_types', 'tab_syntaxes', 'current_group_only',
                           'confirmation_prompt', 'hours', 'exclude_active_tabs'}
            unexpected_args = set(kwargs.keys()) - expected_args
            if unexpected_args:
                print(f"JornCloseTabs: Warning - Unexpected arguments received: {unexpected_args}")
                print(f"JornCloseTabs: These arguments will be ignored.")

            self.last_args = {
                "current_group_only": current_group_only,
                "tab_types": tab_types,
                "tab_syntaxes": tab_syntaxes,
                "confirmation_prompt": confirmation_prompt,
                "hours": hours,
                "exclude_active_tabs": exclude_active_tabs,
            }

            # Main logic with comprehensive error handling
            tab_views = []
            try:
                if 'all' in tab_types:
                    tab_views = self.get_tab_views(current_group_only)
                else:
                    tab_views = self.get_tab_views(current_group_only)
                    tab_views = self.find_matching_tabs(tab_views, tab_types, tab_syntaxes, hours)
                if exclude_active_tabs:
                    tab_views = self.filter_active_tabs(tab_views)
            except Exception as e:
                print(f"JornCloseTabs: Error retrieving tabs: {e}")
                print(f"JornCloseTabs: {traceback.format_exc()}")
                tab_views = []

            # Proceed with closing tabs if any were found
            if tab_views:
                try:
                    if not confirmation_prompt or self.confirm_closing_tabs(tab_views, len(tab_views)):
                        self.close_tabs(tab_views)
                except Exception as e:
                    print(f"JornCloseTabs: Error during tab closing process: {e}")
                    print(f"JornCloseTabs: {traceback.format_exc()}")
            else:
                print("JornCloseTabs: No tabs found matching the specified criteria.")

        except Exception as e:
            # Ultimate fallback - catch any unexpected errors in the main run method
            print(f"JornCloseTabs: Critical error in run method: {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            sublime.error_message(f"JornCloseTabs encountered an error: {str(e)}")
            return

    # Tab retrieval
    def get_tab_views(self, current_group_only):
        """
        Safely retrieve tab views with comprehensive error handling.
        """
        try:
            if not self.window:
                print("JornCloseTabs: Error - No window available")
                return []

            if current_group_only:
                try:
                    active_group = self.window.active_group()
                    if active_group is None:
                        print("JornCloseTabs: Warning - No active group found, falling back to all views")
                        return self.window.views()
                    return self.window.views_in_group(active_group)
                except Exception as e:
                    print(f"JornCloseTabs: Error getting views in active group: {e}")
                    print(f"JornCloseTabs: Falling back to all views")
                    return self.window.views()
            else:
                return self.window.views()
        except Exception as e:
            print(f"JornCloseTabs: Error getting tab views: {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            return []

    def filter_active_tabs(self, tab_views):
        """
        Filter out active tabs from the list with comprehensive error handling.
        """
        try:
            if not self.window or not tab_views:
                return tab_views

            active_views = set()
            try:
                num_groups = self.window.num_groups()
                for group_index in range(num_groups):
                    try:
                        active_view = self.window.active_view_in_group(group_index)
                        if active_view:
                            active_views.add(active_view)
                    except Exception as e:
                        print(f"JornCloseTabs: Error getting active view for group {group_index}: {e}")
                        continue
            except Exception as e:
                print(f"JornCloseTabs: Error iterating through groups: {e}")
                print(f"JornCloseTabs: {traceback.format_exc()}")
                return tab_views

            # Filter out active views
            filtered_views = []
            for view in tab_views:
                try:
                    if view not in active_views:
                        filtered_views.append(view)
                except Exception as e:
                    print(f"JornCloseTabs: Error checking if view is active: {e}")
                    # When in doubt, include the view to be safe
                    filtered_views.append(view)

            return filtered_views
        except Exception as e:
            print(f"JornCloseTabs: Error in filter_active_tabs: {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            return tab_views  # Return original list if filtering fails

    # Tab matching
    def find_matching_tabs(self, tab_views, tab_types, tab_syntaxes, hours):
        """
        Find tabs that match the specified conditions with comprehensive error handling.
        """
        if not tab_views:
            return []

        matching_tabs = []
        for view in tab_views:
            try:
                if not view:  # Skip None views
                    continue

                if self.tab_matches_conditions(view, tab_types, tab_syntaxes, hours):
                    matching_tabs.append(view)
            except Exception as e:
                tab_info = self.get_tab_info(view)
                print(f"JornCloseTabs: Error processing tab '{tab_info}': {e}")
                print(f"JornCloseTabs: {traceback.format_exc()}")
                continue  # Skip this tab and continue with others
        return matching_tabs

    def tab_matches_conditions(self, view, tab_types, tab_syntaxes, hours):
        """
        Check if a tab matches the specified conditions with comprehensive error handling.
        """
        try:
            if not view:
                return False

            # Check both tab type and syntax conditions
            type_match = self.matches_tab_type(view, tab_types, hours)
            syntax_match = self.matches_syntax(view, tab_syntaxes)

            return type_match and syntax_match
        except Exception as e:
            tab_info = self.get_tab_info(view)
            print(f"JornCloseTabs: Error matching conditions for tab '{tab_info}': {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            return False  # If there's an error, don't match this tab

    def matches_tab_type(self, view, tab_types, hours):
        """
        Check if a view matches any of the specified tab types with comprehensive error handling.
        """
        if not tab_types:
            return True  # No type restrictions

        for tab_type in tab_types:
            try:
                if tab_type == "not_modified_in_hours":
                    if self.is_tab_type_not_modified_in_hours(view, hours):
                        return True
                else:
                    # Dynamically get the check function
                    check_func = getattr(self, "is_tab_type_" + tab_type, None)
                    if check_func is not None:
                        try:
                            if check_func(view):
                                return True
                        except Exception as e:
                            tab_info = self.get_tab_info(view)
                            print(f"JornCloseTabs: Error in '{tab_type}' check for tab '{tab_info}': {e}")
                            print(f"JornCloseTabs: {traceback.format_exc()}")
                            continue  # Skip this check and continue with others
                    else:
                        print(f'JornCloseTabs: Unsupported tab type: "{tab_type}". Valid tab-types are: {self.valid_tab_types}')
            except Exception as e:
                tab_info = self.get_tab_info(view)
                print(f"JornCloseTabs: Error processing tab type '{tab_type}' for tab '{tab_info}': {e}")
                print(f"JornCloseTabs: {traceback.format_exc()}")
                continue
        return False

    def matches_syntax(self, view, tab_syntaxes):
        """
        Check if a view matches any of the specified syntaxes with comprehensive error handling.
        """
        if not tab_syntaxes:
            return True  # No syntax restrictions

        try:
            if not view:
                return False

            view_settings = view.settings()
            if not view_settings:
                print("JornCloseTabs: Warning - No settings available for view")
                return False

            tab_syntax = view_settings.get('syntax', '')
            if not tab_syntax:
                # If no syntax is set, consider it a match only if empty string is in the search list
                return '' in tab_syntaxes

            # Check if any of the specified syntaxes match
            for syntax in tab_syntaxes:
                try:
                    if syntax.lower() in tab_syntax.lower():
                        return True
                except (AttributeError, TypeError) as e:
                    print(f"JornCloseTabs: Error comparing syntax '{syntax}' with tab syntax '{tab_syntax}': {e}")
                    continue

            return False
        except Exception as e:
            tab_info = self.get_tab_info(view)
            print(f"JornCloseTabs: Error matching syntax for tab '{tab_info}': {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            return False

    # Tab contains less than 1 character (stripped of whitespaces)
    def is_tab_type_empty(self, view):
        try:
            return len(view.substr(sublime.Region(0, view.size())).strip()) <= 1
        except Exception as e:
            print(f"Error checking if tab is empty: {e}")
            print(traceback.format_exc())
            return False

    # Tab has been saved
    def is_tab_type_saved(self, view):
        try:
            return view.file_name() and not view.is_dirty() and os.path.exists(view.file_name())
        except Exception as e:
            print(f"Error checking if tab is saved: {e}")
            print(traceback.format_exc())
            return False

    # Tab's file has been moved or deleted
    def is_tab_type_deleted(self, view):
        try:
            return view.file_name() and not os.path.exists(view.file_name())
        except Exception as e:
            print(f"Error checking if tab is deleted: {e}")
            print(traceback.format_exc())
            return False

    # Tab has -never- been saved (to a file)
    def is_tab_type_notsaved(self, view):
        try:
            return not view.file_name()
        except Exception as e:
            print(f"Error checking if tab is not saved: {e}")
            print(traceback.format_exc())
            return False

    # Tab has unsaved changes
    def is_tab_type_modified(self, view):
        try:
            return view.file_name() and view.is_dirty() and os.path.exists(view.file_name())
        except Exception as e:
            print(f"Error checking if tab is modified: {e}")
            print(traceback.format_exc())
            return False

    # Tab is not in the current project
    def is_tab_type_not_in_project(self, view):
        try:
            file_name = view.file_name()
            if not file_name:
                return False
            project_folders = self.window.folders()
            return not any(file_name.startswith(folder) for folder in project_folders)
        except Exception as e:
            print(f"Error checking if tab is not in project: {e}")
            print(traceback.format_exc())
            return False

    # Tab is inside folder: .backups
    def is_tab_type_inside_backups(self, view):
        try:
            file_name = view.file_name()
            if not file_name:
                return False
            return ".backups" in file_name
        except Exception as e:
            print(f"Error checking if tab is inside backups: {e}")
            print(traceback.format_exc())
            return False

    # Tab is inside folder: .tmp
    def is_tab_type_inside_tmp(self, view):
        try:
            file_name = view.file_name()
            if not file_name:
                return False
            return ".tmp" in file_name
        except Exception as e:
            print(f"Error checking if tab is inside tmp: {e}")
            print(traceback.format_exc())
            return False

    # Tab has the same extension
    def is_tab_type_same_extension(self, view):
        try:
            active_view = self.window.active_view()
            if not active_view or not active_view.file_name() or not view.file_name():
                return False
            return os.path.splitext(active_view.file_name())[1] == os.path.splitext(view.file_name())[1]
        except Exception as e:
            print(f"Error checking if tab has the same extension: {e}")
            print(traceback.format_exc())
            return False

    # Tab has not been modified in x hours
    def is_tab_type_not_modified_in_hours(self, view, hours):
        try:
            if not view.file_name() or hours is None:
                return False
            last_modified_time = os.path.getmtime(view.file_name())
            current_time = time.time()
            return (current_time - last_modified_time) / 3600 > hours
        except FileNotFoundError:
            # File was deleted after the check
            print(f"File not found when checking modification time: {view.file_name()}")
            return False
        except Exception as e:
            print(f"Error checking if tab was not modified in hours: {e}")
            print(traceback.format_exc())
            return False

    def get_tab_info(self, view):
        """
        Get a safe string representation of a tab for logging and display purposes.
        """
        try:
            if not view:
                return 'None/Invalid View'

            # Try to get file name first
            try:
                file_name = view.file_name()
                if file_name:
                    return os.path.basename(file_name)
            except Exception as e:
                print(f"JornCloseTabs: Error getting file name: {e}")

            # Fall back to character count
            try:
                size = view.size()
                return f'{size} characters'
            except Exception as e:
                print(f"JornCloseTabs: Error getting view size: {e}")

            # Ultimate fallback
            return 'Unknown Tab'
        except Exception as e:
            print(f"JornCloseTabs: Error getting tab info: {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            return 'Error Getting Tab Info'

    def build_prompt(self, tabs_to_close, total_tabs):
        """
        Build a confirmation prompt message with comprehensive error handling.
        """
        try:
            if not tabs_to_close:
                return "No tabs selected for closing."

            prompt_header = f'You are about to close {len(tabs_to_close)}/{total_tabs} tabs:\n'

            # Build the list of tabs, with error handling for each tab
            tab_lines = []
            for i, tab in enumerate(tabs_to_close):
                try:
                    tab_info = self.get_tab_info(tab)
                    tab_lines.append(f'-> Tab {i+1}: {tab_info}\n')
                except Exception as e:
                    print(f"JornCloseTabs: Error getting info for tab {i+1}: {e}")
                    tab_lines.append(f'-> Tab {i+1}: Error getting tab info\n')

            prompt_message = ''.join(tab_lines)
            prompt_footer = '\nThis cannot be undone, are you sure?'
            return prompt_header + prompt_message + prompt_footer
        except Exception as e:
            print(f"JornCloseTabs: Error building prompt: {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            return "Are you sure you want to close the selected tabs?"

    def confirm_closing_tabs(self, tabs_to_close, total_tabs):
        """
        Display confirmation dialog with comprehensive error handling.
        """
        try:
            prompt_message = self.build_prompt(tabs_to_close, total_tabs)
            return sublime.ok_cancel_dialog(prompt_message)
        except Exception as e:
            print(f"JornCloseTabs: Error displaying confirmation dialog: {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            # Default to not confirming if there's an error
            return False

    def close_tabs(self, tabs_to_close):
        """
        Close the specified tabs with comprehensive error handling and safety checks.
        """
        if not tabs_to_close:
            print("JornCloseTabs: No tabs to close")
            return

        try:
            confirmation_was_skipped = not self.last_args.get("confirmation_prompt", True)
            closed_count = 0
            closed_tabs = set()
            skipped_tabs = set()
            error_tabs = set()

            for tab in tabs_to_close:
                try:
                    if not tab:  # Skip None tabs
                        continue

                    tab_info = self.get_tab_info(tab)

                    # Failsafe: Only skip if confirmation was explicitly false AND tab is dirty AND file exists
                    try:
                        if (confirmation_was_skipped and
                            tab.is_dirty() and
                            tab.file_name() and
                            os.path.exists(tab.file_name())):
                            skipped_tabs.add(tab_info)
                            continue  # Skip closing this specific tab
                    except Exception as e:
                        print(f"JornCloseTabs: Error checking tab safety for '{tab_info}': {e}")
                        # When in doubt, skip the tab for safety
                        skipped_tabs.add(tab_info)
                        continue

                    # Attempt to close the tab
                    try:
                        tab.set_scratch(True)
                        tab.close()
                        closed_tabs.add(tab_info)
                        closed_count += 1
                    except Exception as e:
                        print(f"JornCloseTabs: Error closing tab '{tab_info}': {e}")
                        error_tabs.add(tab_info)
                        continue

                except Exception as e:
                    tab_info = self.get_tab_info(tab)
                    print(f"JornCloseTabs: Error processing tab '{tab_info}': {e}")
                    print(f"JornCloseTabs: {traceback.format_exc()}")
                    error_tabs.add(tab_info)
                    continue

            # Report results
            if skipped_tabs:
                print(f"\nJornCloseTabs: Skipped {len(skipped_tabs)} tabs (safety failsafe)")
                print(f"JornCloseTabs: Skipped tabs: {skipped_tabs}")

            if closed_tabs:
                print(f"\nJornCloseTabs: Successfully closed {len(closed_tabs)} tabs")
                print(f"JornCloseTabs: Closed tabs: {closed_tabs}")

            if error_tabs:
                print(f"\nJornCloseTabs: Failed to close {len(error_tabs)} tabs due to errors")
                print(f"JornCloseTabs: Error tabs: {error_tabs}")

        except Exception as e:
            print(f"JornCloseTabs: Critical error in close_tabs method: {e}")
            print(f"JornCloseTabs: {traceback.format_exc()}")
            sublime.error_message(f"JornCloseTabs: Failed to close tabs due to error: {str(e)}")

        return
