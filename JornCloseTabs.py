
# [Close Tabs]
import os
import sublime
import sublime_plugin
import time
import math
import traceback

class JornCloseTabsCommand(sublime_plugin.WindowCommand):
    valid_tab_types = [
        "all", "empty", "saved", "deleted", "notsaved",
        "modified", "not_in_project", "inside_backups",
        "inside_tmp", "same_extension", "not_modified_in_hours"
    ]
    last_args = {}

    def run(self, tab_types=[], tab_syntaxes=[], current_group_only=True, confirmation_prompt=True, hours=None, exclude_active_tabs=False):
        self.last_args = {
            "current_group_only": current_group_only,
            "tab_types": tab_types,
            "tab_syntaxes": tab_syntaxes,
            "confirmation_prompt": confirmation_prompt,
            "hours": hours,
            "exclude_active_tabs": exclude_active_tabs,
        }

        try:
            if 'all' in tab_types:
                tab_views = self.get_tab_views(current_group_only)
            else:
                tab_views = self.get_tab_views(current_group_only)
                tab_views = self.find_matching_tabs(tab_views, tab_types, tab_syntaxes, hours)
            if exclude_active_tabs:
                tab_views = self.filter_active_tabs(tab_views)
        except Exception as e:
            print(f"Error retrieving tabs: {e}")
            print(traceback.format_exc())
            tab_views = []

        if tab_views and (not confirmation_prompt or self.confirm_closing_tabs(tab_views, len(tab_views))):
            self.close_tabs(tab_views)

    # Tab retrieval
    def get_tab_views(self, current_group_only):
            try:
                return self.window.views_in_group(self.window.active_group()) if current_group_only else self.window.views()
            except Exception as e:
                print(f"Error getting tab views: {e}")
                print(traceback.format_exc())
                return []

    def filter_active_tabs(self, tab_views):
        active_views = set()
        for group_index in range(self.window.num_groups()):
            active_view = self.window.active_view_in_group(group_index)
            if active_view:
                active_views.add(active_view)
        return [view for view in tab_views if view not in active_views]

    # Tab matching
    def find_matching_tabs(self, tab_views, tab_types, tab_syntaxes, hours):
        matching_tabs = []
        for view in tab_views:
            try:
                if self.tab_matches_conditions(view, tab_types, tab_syntaxes, hours):
                    matching_tabs.append(view)
            except Exception as e:
                print(f"Error processing tab '{self.get_tab_info(view)}': {e}")
                print(traceback.format_exc())
                continue  # Skip this tab and continue with others
        return matching_tabs

    def tab_matches_conditions(self, view, tab_types, tab_syntaxes, hours):
        try:
            return self.matches_tab_type(view, tab_types, hours) and self.matches_syntax(view, tab_syntaxes)
        except Exception as e:
            print(f"Error matching conditions for tab '{self.get_tab_info(view)}': {e}")
            print(traceback.format_exc())
            return False  # If there's an error, don't match this tab

    def matches_tab_type(self, view, tab_types, hours):
        for tab_type in tab_types:
            if tab_type == "not_modified_in_hours":
                if self.is_tab_type_not_modified_in_hours(view, hours):
                    return True
            else:
                check_func = getattr(self, "is_tab_type_" + tab_type, None)
                if check_func is not None:
                    try:
                        if check_func(view):
                            return True
                    except Exception as e:
                        print(f"Error in '{tab_type}' check for tab '{self.get_tab_info(view)}': {e}")
                        print(traceback.format_exc())
                        continue  # Skip this check and continue with others
                else:
                    print('Unsupported: ["{}"]. Valid tab-types are: ["{}"]'.format(
                        tab_type, '", "'.join(self.valid_tab_types)))
        return False

    def matches_syntax(self, view, tab_syntaxes):
        if not tab_syntaxes:
            return True
        try:
            tab_syntax = view.settings().get('syntax')
            return any(syntax.lower() in tab_syntax.lower() for syntax in tab_syntaxes)
        except Exception as e:
            print(f"Error matching syntax for tab '{self.get_tab_info(view)}': {e}")
            print(traceback.format_exc())
            return False

    # Tab contains less than 1 character (stripped of whitespaces)
    def is_tab_type_empty(self, view):
        try:
            return len(view.substr(sublime.Region(0, view.size())).strip()) <= 1
        except Exception as e:
            print(f"Error checking if tab is empty: {e}")
            print(traceback.format_exc())
            return False

    # Tab has been saved
    def is_tab_type_saved(self, view):
        try:
            return view.file_name() and not view.is_dirty() and os.path.exists(view.file_name())
        except Exception as e:
            print(f"Error checking if tab is saved: {e}")
            print(traceback.format_exc())
            return False

    # Tab's file has been moved or deleted
    def is_tab_type_deleted(self, view):
        try:
            return view.file_name() and not os.path.exists(view.file_name())
        except Exception as e:
            print(f"Error checking if tab is deleted: {e}")
            print(traceback.format_exc())
            return False

    # Tab has -never- been saved (to a file)
    def is_tab_type_notsaved(self, view):
        try:
            return not view.file_name()
        except Exception as e:
            print(f"Error checking if tab is not saved: {e}")
            print(traceback.format_exc())
            return False

    # Tab has unsaved changes
    def is_tab_type_modified(self, view):
        try:
            return view.file_name() and view.is_dirty() and os.path.exists(view.file_name())
        except Exception as e:
            print(f"Error checking if tab is modified: {e}")
            print(traceback.format_exc())
            return False

    # Tab is not in the current project
    def is_tab_type_not_in_project(self, view):
        try:
            file_name = view.file_name()
            if not file_name:
                return False
            project_folders = self.window.folders()
            return not any(file_name.startswith(folder) for folder in project_folders)
        except Exception as e:
            print(f"Error checking if tab is not in project: {e}")
            print(traceback.format_exc())
            return False

    # Tab is inside folder: .backups
    def is_tab_type_inside_backups(self, view):
        try:
            file_name = view.file_name()
            if not file_name:
                return False
            return ".backups" in file_name
        except Exception as e:
            print(f"Error checking if tab is inside backups: {e}")
            print(traceback.format_exc())
            return False

    # Tab is inside folder: .tmp
    def is_tab_type_inside_tmp(self, view):
        try:
            file_name = view.file_name()
            if not file_name:
                return False
            return ".tmp" in file_name
        except Exception as e:
            print(f"Error checking if tab is inside tmp: {e}")
            print(traceback.format_exc())
            return False

    # Tab has the same extension
    def is_tab_type_same_extension(self, view):
        try:
            active_view = self.window.active_view()
            if not active_view or not active_view.file_name() or not view.file_name():
                return False
            return os.path.splitext(active_view.file_name())[1] == os.path.splitext(view.file_name())[1]
        except Exception as e:
            print(f"Error checking if tab has the same extension: {e}")
            print(traceback.format_exc())
            return False

    # Tab has not been modified in x hours
    def is_tab_type_not_modified_in_hours(self, view, hours):
        try:
            if not view.file_name() or hours is None:
                return False
            last_modified_time = os.path.getmtime(view.file_name())
            current_time = time.time()
            return (current_time - last_modified_time) / 3600 > hours
        except FileNotFoundError:
            # File was deleted after the check
            print(f"File not found when checking modification time: {view.file_name()}")
            return False
        except Exception as e:
            print(f"Error checking if tab was not modified in hours: {e}")
            print(traceback.format_exc())
            return False

    def get_tab_info(self, view):
        try:
            return os.path.basename(view.file_name()) if view.file_name() else '{} characters'.format(view.size())
        except Exception as e:
            print(f"Error getting tab info: {e}")
            print(traceback.format_exc())
            return 'Unknown'

    def build_prompt(self, tabs_to_close, total_tabs):
        try:
            prompt_header = 'You are about to close {}/{} tabs:\n'.format(len(tabs_to_close), total_tabs)
            prompt_message = ''.join('-> Tab {}: {}\n'.format(i+1, self.get_tab_info(tab)) for i, tab in enumerate(tabs_to_close))
            prompt_footer = '\nThis cannot be undone, are you sure?'
            return prompt_header + prompt_message + prompt_footer
        except Exception as e:
            print(f"Error building prompt: {e}")
            print(traceback.format_exc())
            return "Are you sure you want to close the selected tabs?"

    def confirm_closing_tabs(self, tabs_to_close, total_tabs):
        try:
            prompt_message = self.build_prompt(tabs_to_close, total_tabs)
            return sublime.ok_cancel_dialog(prompt_message)
        except Exception as e:
            print(f"Error displaying confirmation dialog: {e}")
            print(traceback.format_exc())
            return False

    def close_tabs(self, tabs_to_close):
        confirmation_was_skipped = not self.last_args.get("confirmation_prompt", True) # Default to True if not found
        closed_count = 0
        closed_tabs = set()
        skipped_tabs = set()

        for tab in tabs_to_close:
            try:
                # Failsafe: Only skip if confirmation was explicitly false AND tab is dirty AND file exists
                if confirmation_was_skipped and tab.is_dirty() and tab.file_name() and os.path.exists(tab.file_name()):
                    skipped_tabs.add(self.get_tab_info(tab))
                    continue # Skip closing this specific tab
                # Catch and close
                closed_tabs.add(self.get_tab_info(tab))
                tab.set_scratch(True)
                tab.close()
                closed_count += 1
            except Exception as e:
                print(f"Error closing tab '{self.get_tab_info(tab)}': {e}")
                print(traceback.format_exc())
                continue

        if skipped_tabs:
            print('\n')
            print(f"Skipped {len(skipped_tabs)} tabs (through failsafe trigger)")
            print(f"Skipped {skipped_tabs}")

        if closed_tabs:
            print('\n')
            print(f"Closed {len(closed_tabs)} tabs")
            print(f"Closed {closed_tabs}")

        return
