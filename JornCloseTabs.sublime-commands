[
    //     "caption": "Jorn: Close Tabs: Cleanup tabs older than \t dm:<last1hours",

    {
        "caption": "Jorn - Close Tabs: [ < 15 Minutes > ] \t <Cleanup Tabs> | dm:<last15minutes",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": false,
            // "tab_types": ["not_modified_in_hours", "empty", "deleted"],
            "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": false, // set to true to preview
            "hours": 0.25,
            "exclude_active_tabs": false, // if true: only affect background tabs
        }
    },
    {
        "caption": "Jorn - Close Tabs: [ < Hour > ] \t <Cleanup Tabs> | dm:<last1hours",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": false,
            // "tab_types": ["not_modified_in_hours", "empty", "deleted"],
            "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": false, // set to true to preview
            "hours": 1,
            "exclude_active_tabs": false, // if true: only affect background tabs
        }
    },
    {
        "caption": "Jorn - Close Tabs: [ < Day > ] \t <Cleanup Tabs> | dm:<last24hours",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": false,
            // "tab_types": ["not_modified_in_hours", "empty", "deleted"],
            "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": false, // set to true to preview
            "hours": 24,
            "exclude_active_tabs": false, // if true: only affect background tabs
        }
    },
    {
        "caption": "Jorn - Close Tabs: [ < Week > ] \t <Cleanup Tabs> | dm:<last1week",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": false,
            // "tab_types": ["not_modified_in_hours", "empty", "deleted"],
            "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": false, // set to true to preview
            "hours": 168,
            "exclude_active_tabs": false, // if true: only affect background tabs
        }
    },

    {
        "caption": "Jorn - Close Tabs: [ < All Inactive Background-Tabs > ] \t <Cleanup Background Tabs> | *",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": false,
            "tab_types": ["empty", "deleted", "saved"],
            "tab_syntaxes": [],
            "confirmation_prompt": false,
            "exclude_active_tabs": true,

        }
    },

]



    // "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
