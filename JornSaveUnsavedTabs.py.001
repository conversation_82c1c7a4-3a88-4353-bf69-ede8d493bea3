
# '2024.uke47.kl08_unsaved_a.txt' # increment set to "a.txt" because "2024.uke47.h08.a.txt" doesn't exist
# '2024.uke47.kl08_unsaved_b.txt' # incremented to "b.txt" since "2024.uke47.h08.a.txt" exists
# '2024.uke47.kl24_unsaved_a.json' # increment set to "a.json" because "2024.uke47.h08.a.json" doesn't exist
# '2024.uke47.kl24_unsaved_a.py' # increment set to "a.py" because "2024.uke47.h08.a.py" doesn't exist
# '2024.uke47.kl24_unsaved_a.txt' # increment set to "a.txt" because "2024.uke47.h24.a.txt" doesn't exist
# '2024.uke47.kl24_unsaved_b.txt' # incremented to "b.txt" since "2024.uke47.h24.a.txt" exists
# '2024.uke47.kl24_unsaved_c.txt' # incremented to "c.txt" since "2024.uke47.h24.b.txt" exists
# '2024.uke48.kl09_unsaved_a.txt'
# '2024.uke48.kl09_unsaved_a.txt' # ...
# '2024.uke48.kl09_unsaved_b.txt'
# '2024.uke48.kl09_unsaved_c.txt'
# '2024.uke48.kl09_unsaved_d.txt'
# '2024.uke48.kl13_unsaved_a.md'
# '2024.uke48.kl13_unsaved_b.md'

import os
import datetime
import hashlib
import textwrap
import sublime
import sublime_plugin

# Map of Sublime Text syntax to file extensions
EXTENSION_MAPPING = {
    "Bash": ".bash", "Batch File": ".bat", "C": ".c", "C++": ".cpp", "Command Prompt": ".cmd",
    "CSS": ".css", "Dockerfile": ".dockerfile", "Go": ".go", "Haskell": ".hs", "HTML": ".html",
    "INI": ".ini", "Java": ".java", "JavaScript": ".js", "JSON": ".json", "Kotlin": ".kt",
    "Less": ".less", "Lua": ".lua", "Makefile": ".mk", "Markdown": ".md", "MaxScript": ".ms",
    "Nginx Config": ".conf", "NSS": ".nss", "Objective-C": ".m", "Perl": ".pl", "PHP": ".php",
    "Plain Text": ".txt", "PowerShell": ".ps1", "Python": ".py", "Ruby": ".rb", "Rust": ".rs",
    "Scala": ".scala", "SCSS": ".scss", "ShellScript": ".sh", "SQL": ".sql", "Swift": ".swift",
    "TypeScript": ".ts", "Visual Basic": ".vb", "XML": ".xml", "YAML": ".yaml",
}

class JornSaveAllUnsavedTabsCommand(sublime_plugin.WindowCommand):
    def run(self, target_directory=None, auto_open_target_directory=False, auto_close_tabs=False):
        # Ensure the target directory exists
        target_directory = self.ensure_directory(target_directory)

        # Prepare existing file data
        existing_files, existing_hashes = self.scan_existing_files(target_directory)

        # Generate filename prefix
        filename_prefix = self.generate_filename_prefix()

        # Process unsaved tabs
        unsaved_tabs = [view for view in self.window.views() if not view.file_name()]
        for view in unsaved_tabs:
            self.process_tab(view, target_directory, existing_files, existing_hashes, filename_prefix, auto_close_tabs)

        # Open target directory if requested
        if auto_open_target_directory:
            self.window.run_command("open_dir", {"dir": target_directory})

    def ensure_directory(self, target_directory):
        """Ensure the backup directory exists."""
        if not target_directory:
            target_directory = self.default_backup_directory()
        os.makedirs(target_directory, exist_ok=True)
        return target_directory

    def default_backup_directory(self):
        """Determine the default backup directory."""
        project_folders = self.window.folders()
        base_dir = os.path.dirname(os.path.abspath(__file__))
        if project_folders:
            project_name = os.path.basename(project_folders[0])
            return os.path.join(base_dir, "unsaved_tabs_backup", project_name)
        return os.path.join(base_dir, "unsaved_tabs_backup")

    def scan_existing_files(self, target_directory):
        """Retrieve filenames and content hashes from the backup directory."""
        existing_files = {file for _, _, files in os.walk(target_directory) for file in files}
        existing_hashes = set()
        for file in os.listdir(target_directory):
            file_path = os.path.join(target_directory, file)
            if os.path.isfile(file_path):
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                        existing_hashes.add(hashlib.md5(content.encode()).hexdigest())
                except Exception:
                    pass
        return existing_files, existing_hashes

    # def generate_filename_prefix(self):
    #     """Create a timestamp-based prefix for filenames."""
    #     now = datetime.datetime.now()
    #     year, week, _ = now.isocalendar()
    #     return f"{year}.uke{week:02}_kl{now.hour:02}"
    # CHANGING PATTERN: "{date}-{session}-{i}-{userid}" "2025.02.22-001-a-dsk" # session1.window1
    def generate_filename_prefix(self):
        """Create a timestamp-based prefix for filenames."""
        now = datetime.datetime.now()
        year, week, _ = now.isocalendar()
        return f"{year}.uke{week:02}_kl{now.hour:02}"

    def process_tab(self, view, target_directory, existing_files, existing_hashes, filename_prefix, auto_close_tabs):
        """Save and optionally close an unsaved tab."""
        content = view.substr(sublime.Region(0, view.size())).strip()
        if not content:
            view.set_scratch(True)
            view.close()
            return

        content_hash = hashlib.md5(content.encode()).hexdigest()
        if content_hash in existing_hashes:
            sublime.status_message("Duplicate content detected. Skipping tab.")
            return

        extension = self.determine_extension(view)
        filename = self.create_unique_filename(filename_prefix, extension, existing_files, content)
        self.save_tab_content(view, content, target_directory, filename, existing_hashes, auto_close_tabs)

    def determine_extension(self, view):
        """Determine the file extension based on the view's syntax."""
        syntax = (view.settings().get("syntax") or "Plain Text").lower()
        for key, ext in EXTENSION_MAPPING.items():
            if key.lower() in syntax.lower() and len(key.lower()) >= 3:
                return ext
        return ".txt"

    def create_unique_filename(self, prefix, extension, existing_files, content):
        """Generate a unique filename to avoid conflicts."""
        wrapped_lines = len(textwrap.fill(content, width=100).splitlines())
        suffix = 'a'
        while any(f.startswith(f"{prefix}_{suffix}_len") for f in existing_files):
            suffix = chr(ord(suffix) + 1)
        return f"{prefix}_{suffix}_len{wrapped_lines}{extension}"

    def save_tab_content(self, view, content, directory, filename, hashes, auto_close_tabs):
        """Write tab content to a file and optionally close the tab."""
        path = os.path.join(directory, filename)
        try:
            with open(path, "w", encoding="utf-8") as file:
                file.write(content)
            hashes.add(hashlib.md5(content.encode()).hexdigest())
            view.set_scratch(True)
            if auto_close_tabs:
                view.close()
        except Exception as e:
            sublime.status_message(f"Error saving tab: {e}")
