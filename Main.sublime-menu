[
    {
        // Ribbon-Menu: Preferences -> Package Settings
        "caption": "Preferences",
        "mnemonic": "n",
        "id": "preferences",
        "children":
        [
            {
                "caption": "Package Settings",
                "mnemonic": "P",
                "id": "package-settings",
                "children":
                [
                    {
                        // Jorn Tools
                        "caption": "Jorn Tools",
                        "mnemonic": "J",
                        "id": "jorn-tools",
                        "children": [
                            {
                                "caption": "Jorn_TabUtils",
                                "children":
                                [
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Main.sublime-menu",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_TabUtils/Main.sublime-menu"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Jorn_TabUtils.py",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_TabUtils/Jorn_TabUtils.py"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Settings",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_TabUtils/Jorn_TabUtils.sublime-settings"},
                                    },
                                    {
                                        "caption": "Settings – User",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/User/Default.sublime-settings"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Keymap",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_TabUtils/Jorn_TabUtils.sublime-keymap"},
                                    },
                                    {
                                        "caption": "Keymap – User",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/User/Default (Windows).sublime-keymap"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Commands",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_TabUtils/Jorn_TabUtils.sublime-commands"},
                                    },
                                    {
                                        "caption": "Commands – User",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/User/Default (Windows).sublime-commands"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Open Folder -> Todo",
                                        "args": {"cmd": ["explorer.exe", "${packages}/Jorn_TabUtils"]}
                                        // "command": "jorn_open_directory",
                                        // "args": {"group": -1, "index": -1 },
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    },
    {
        // Ribbon-Menu: Jorn Tools
        "caption": "Jorn Tools",
        "mnemonic": "J",
        "id": "jorn-tools",
        "children": [
            {
                "caption": "Jorn_TabUtils",
                "children":
                [
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Main.sublime-menu",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_TabUtils/Main.sublime-menu"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Jorn_TabUtils.py",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_TabUtils/Jorn_TabUtils.py"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Settings",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_TabUtils/Jorn_TabUtils.sublime-settings"},
                    },
                    {
                        "caption": "Settings – User",
                        "command": "open_file",
                        "args": {"file": "${packages}/User/Default.sublime-settings"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Keymap",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_TabUtils/Jorn_TabUtils.sublime-keymap"},
                    },
                    {
                        "caption": "Keymap – User",
                        "command": "open_file",
                        "args": {"file": "${packages}/User/Default (Windows).sublime-keymap"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Commands",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_TabUtils/Jorn_TabUtils.sublime-commands"},
                    },
                    {
                        "caption": "Commands – User",
                        "command": "open_file",
                        "args": {"file": "${packages}/User/Default (Windows).sublime-commands"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Open Folder -> Todo",
                        "args": {"cmd": ["explorer.exe", "${packages}/Jorn_TabUtils"]}
                        // "command": "jorn_open_directory",
                        // "args": {"group": -1, "index": -1 },
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                ]
            }
        ]
    }
]

