// MenuItem -> ChildItems
// "caption": "something \t [...]   ",

// Unicode font icons:
// "caption": "something \t ○   ",
// "caption": "something \t ▣   ",
// "caption": "something \t ▷   ",
// "caption": "something \t  △   ",
// "caption": "something \t ⧋   ",
// "caption": "something \t ◭   ",
// "caption": "something \t ✕   ",
// "caption": "something \t ⴱ   "

// "caption": "something \t 🝕   ",
// "caption": "something \t 🗹   ",
// "caption": "something \t  ⃞   ",
// "caption": "something \t ◻   ",

// "caption": "something \t ⌕   ",

// "caption": "something \t ○   ",
// "caption": "something \t ●   ",

// "caption": "something \t ☐   ",
// "caption": "something \t ▣   ",
// "caption": "something \t ☑   ",
// "caption": "something \t ☒   ",

// "caption": "something \t ‐   ",

// [CONTINOUS LINES]
// _______________________
// ───────────────────────
// ━━━━━━━━━━━━━━━━━━━━━━━
// ═══════════════════════


// «» 「」 【】 《》 ⧼⧽
// ← → ↑ ↓
// ↔ ↕
// ⌃ ⌄
// ◀ ▶ ▲ ▼
// ◄ ► ◅ ▻
// ➛ ➜ ➔ ➝ ➞ ➟ ➠ ➧ ➨
// ➢ ➣ ➤
// ⟸ ⟹ ⟺
// ⟻ ⟼
// ⮀ ⮂ ⮁ ⮃
// ⮘ ⮚ ⮙ ⮛
// ⮜ ⮞ ⮝ ⮟
// ⯇ ⯈ ⯅ ⯆
// 🠄 🠆 🠅 🠇 ",
// 🠈 🠊 🠉 🠋
// 🠴 🠶 🠵 🠷
// 🡄 🡆 🡅 🡇
// 🡠 🡢 🡡 🡣 🡤 🡥 🡦 🡧
// 🡨 🡪 🡩 🡫 🡬 🡭 🡮 🡯
// 🡰 🡲 🡱 🡳 🡴 🡵 🡶 🡷
// 🡸 🡺 🡹 🡻 🡼 🡽 🡾 🡿



[
    {"caption": "-", "id": "0_separator"},

    {
        "caption": "Jorn - Close Tabs: [ < Week > ] \t <Cleanup Tabs> | dm:<last1week | View",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": true,
            // "tab_types": ["not_modified_in_hours", "empty", "deleted"],
            "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": false, // set to true to preview
            "hours": 168,
            "exclude_active_tabs": true, // only affect background tabs
        }
    },
    {
        "caption": "Jorn - Close Tabs: [ < 2 Days > ] \t <Cleanup Tabs> | dm:<last48hours | View",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": true,
            // "tab_types": ["not_modified_in_hours", "empty", "deleted"],
            "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": false, // set to true to preview
            "hours": 48,
            "exclude_active_tabs": true, // only affect background tabs
        }
    },
    {
        "caption": "Jorn - Close Tabs: [ < Day > ] \t <Cleanup Tabs> | dm:<last24hours | View",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": true,
            // "tab_types": ["not_modified_in_hours", "empty", "deleted"],
            "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": false, // set to true to preview
            "hours": 24,
            "exclude_active_tabs": true, // only affect background tabs
        }
    },
    {
        "caption": "Jorn - Close Tabs: [ < Hour > ] \t <Cleanup Tabs> | dm:<last1minutes | View",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": true,
            // "tab_types": ["not_modified_in_minutes", "empty", "deleted"],
            "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": false, // set to true to preview
            "hours": 1,
            "exclude_active_tabs": true, // only affect background tabs
        }
    },
    {
        "caption": "Jorn - Close Tabs: [ < 15 Minutes > ] \t <Cleanup Tabs> | dm:<last15minutes | View",
        "command": "jorn_close_tabs",
        "args":
        {
            "current_group_only": true,
            // "tab_types": ["not_modified_in_hours", "empty", "deleted"],
            "tab_types": ["not_modified_in_hours", "empty", "deleted", "inside_backups", "inside_tmp", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": false, // set to true to preview
            "hours": 0.25,
            "exclude_active_tabs": false, // if true: only affect background tabs
        }
    },
    {"caption": "-", "id": "0_separator"},
    {
        "command": "jorn_close_tabs",
        "caption": "Jorn - Close Tabs: [ < All Background Tabs > ] \t <Cleanup Inactive Background Tabs> | View",
        "args":
        {
            "current_group_only": true,
            "tab_types": ["empty", "deleted", "saved"],
            "tab_syntaxes": [],
            "confirmation_prompt": false,
            "exclude_active_tabs": true,

        }
    },




    {"caption": "-", "id": "0_separator"},
    {
        "command": "jorn_open_current_folder",
        "caption": "+ Show In Folder \t   ",
        "args": {"group": -1, "index": -1 }
    },
    {
        "command": "jorn_find_in_files_any_extension",
        "caption": "+ Search Directory \t   ",
        "args": {"group": -1, "index": -1 }
    },
    {
        "command": "revert",
        "caption": "+ Discard Changes \t   ",
    },
    // =======================================================

    {"caption": "-", "id": "0_separator"},
    {
        "command": "jorn_copy_base_name",
        "caption": "│ Copy Basename \t 🝕   ",
        //"caption": "─ Copy Basename \t 🝕   ",
        "args": {"group": -1, "index": -1 }
    },
    {
        "command": "jorn_copy_file_name",
        "caption": "│ Copy Filename \t 🝕   ",
        //"caption": "─ Copy Filename \t 🝕   ",
        "args": {"group": -1, "index": -1 }
    },
    {
        "command": "jorn_copy_file_path",
        "caption": "│ Copy Path  \t 🝕   ",
        //"caption": "─ Copy Path  \t 🝕   ",
        "args": {"group": -1, "index": -1 }
    },
    // =======================================================


    {"caption": "-", "id": "0_separator"},
    // {
    //     "caption": "Categorize Tabs",
    //     "command": "jorn_categorize_tabs",
    //     "args":
    //     {
    //         "current_group_only": false,
    //         "tab_types": ["all"],
    //         // "tab_types": ["empty", "inside_backups", "inside_tmp", "deleted", "not_in_project"],
    //         "tab_syntaxes": [],
    //         "confirmation_prompt": true,
    //     }
    // },

    {
        "caption": "Jorn: Categorize and Print Tabs",
        "command": "jorn_categorize_tabs",
        "args": {
            "categorize_and_print": true
        }
    },

    // =======================================================

    {"caption": "-", "id": "0_separator"},
    {
        "command": "jorn_close_tabs",
        "caption": "➤ Close Saved Tabs in View \t : View",
        "args":
        {
            "current_group_only": true,
            "tab_types": ["saved"],
            "tab_syntaxes": [],
            "confirmation_prompt": false
        }
    },

    {
        "command": "jorn_close_tabs",
        "caption": "➤ Close Tabs \t < .deleted | .backups | .tmp | .external >",
        "args":
        {
            "current_group_only": false,
            "tab_types": ["empty", "inside_backups", "inside_tmp", "deleted", "not_in_project"],
            "tab_syntaxes": [],
            "confirmation_prompt": true,
        }
    },
    {"caption": "-", "id": "0_separator"},
    {
        "command": "jorn_save_all_unsaved_tabs",
        "caption": "➤ Backup Unsaved \t < save and closes all unsaved tabs >",
        "args": {
            "target_directory": "",
            "auto_close_tabs": true
        }
    },
    // =======================================================

    // ---------------------------------------------------------------------------------------------------------------------------
    {"caption": "-", "id": "0_separator"},
    {
        "id": "0_separator",
        "caption": "├── Close Tabs \t [...]  ",
        "children": [


            //
            {"caption": "-", "id": "0_separator"},
            {
                "command": "jorn_close_tabs",
                "caption": "Close: .ALL \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["all"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true,
                }
            },
            {
                "command": "jorn_close_tabs",
                "caption": "Close: .extension \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["same_extension"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true
                }
            },
            {
                "command": "jorn_close_tabs",
                "caption": "Close: .saved \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["saved"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": false
                }
            },
            {
                "command": "jorn_close_tabs",
                "caption": "Close: Unsaved \t *",
                "args":
                {
                    "current_group_only": false,
                    // "tab_types": ["empty", "deleted", "notsaved"],
                    "tab_types": ["empty", "notsaved"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true
                }
            },
            //
            //
            {"caption": "-", "id": "0_separator"},


            //
            {"caption": "-", "id": "0_separator"},
            {
                "command": "jorn_close_tabs",
                "caption": ".deleted | .backups | .tmp | .external \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["empty", "inside_backups", "inside_tmp", "deleted", "not_in_project"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": false,
                }
            },

            //
            {"caption": "-", "id": "0_separator"},
            {
                "command": "jorn_close_tabs",
                "caption": ".backups \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["empty", "inside_backups"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true,
                }
            },
            {
                "command": "jorn_close_tabs",
                "caption": ".deleted \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["empty", "deleted"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": false
                }
            },
            {
                "command": "jorn_close_tabs",
                "caption": ".external \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["empty", "not_in_project"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true,
                }
            },
            {
                "command": "jorn_close_tabs",
                "caption": ".unsaved \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["empty", "notsaved"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true
                }
            },
            {"caption": "-", "id": "0_separator"},
            {
                "command": "jorn_close_tabs",
                "caption": "Not Modified This Week \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["not_modified_in_hours"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true,
                    "hours": 168
                }
            },
            {
                "command": "jorn_close_tabs",
                "caption": "Not Modified in Last 24 Hours \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["not_modified_in_hours"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true,
                    "hours": 24
                }
            },
            {
                "command": "jorn_close_tabs",
                "caption": "Not Modified in Last 5 Hours \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["not_modified_in_hours"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true,
                    "hours": 5
                }
            },
            {
                "command": "jorn_close_tabs",
                "caption": "Not Modified in Last 1 Hours \t *",
                "args":
                {
                    "current_group_only": false,
                    "tab_types": ["not_modified_in_hours"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true,
                    "hours": 1
                }
            },
            {"caption": "-", "id": "0_separator"},
            //

            {
                "command": "jorn_close_tabs",
                // "caption": "Close: .ALL \t ... in View",
                // "caption": "Close: .ALL \t {view}",
                // "caption": "Close: .ALL \t | View",
                // "caption": "Close: .ALL \t |-> view",
                "caption": "Close: .ALL \t : in View",
                "args":
                {
                    "current_group_only": true,
                    "tab_types": ["all"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true,
                }
            },

            //
            {
                "command": "jorn_close_tabs",
                // "caption": "Close: .extension \t ... in View",
                // "caption": "Close: .extension \t {view}",
                // "caption": "Close: .extension \t | View",
                // "caption": "Close: .extension \t |-> view",
                "caption": "Close: .extension \t : in View",
                "args":
                {
                    "current_group_only": true,
                    "tab_types": ["same_extension"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true
                }
            },
            {
                "command": "jorn_close_tabs",
                // "caption": "Close: .modified \t ... in View",
                // "caption": "Close: .modified \t {view}",
                // "caption": "Close: .modified \t | View",
                // "caption": "Close: .modified \t |-> view",
                "caption": "Close: .modified \t : in View",
                "args":
                {
                    "current_group_only": true,
                    "tab_types": ["modified"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true
                }
            },
            {
                "command": "jorn_close_tabs",
                // "caption": "Close: .saved \t ... in View",
                // "caption": "Close: .saved \t {view}",
                // "caption": "Close: .saved \t | View",
                // "caption": "Close: .saved \t |-> view",
                "caption": "Close: .saved \t : in View",
                "args":
                {
                    "current_group_only": true,
                    "tab_types": ["saved"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true
                }
            },
            {
                "command": "jorn_close_tabs",
                // "caption": "Close: .unsaved \t ... in View",
                // "caption": "Close: .unsaved \t {view}",
                // "caption": "Close: .unsaved \t | View",
                // "caption": "Close: .unsaved \t |-> view",
                "caption": "Close: .unsaved \t : in View",
                "args":
                {
                    "current_group_only": true,
                    "tab_types": ["empty", "notsaved"],
                    "tab_syntaxes": [],
                    "confirmation_prompt": true
                }
            },



            // {"caption": "-", "id": "0_separator"},
            // {
                // Close All Tabs With The Current Tab's Syntax
                // - Tab does not have the same syntax as the current

                // "command": "jorn_close_tabs",
                // "caption": "Close: .modified Tabs \t ✕",
                // "args":
                // {
                //     "current_group_only": true,
                //     "tab_types": ["modified"],
                //     "tab_syntaxes": [],
                //     "confirmation_prompt": true
                // }
            // },
        ]
    },

    // ---------------------------------------------------------------------------------------------------------------------------
    // [PLUGIN: SortTabs]
    {"caption": "-", "id": "0_separator"},
    {
        "id": "01_Top__SortTabs",
        "caption": "├── Sort Tabs \t [...]  ",
        // "caption": "[Sort Tabs] \t [...]  ",

        // "caption": "[Sort Tabs] -->",
        "children": [
            // Multi-Sorts
            { "command": "sort_tabs_custom1",    "caption":"1: [Directory, Filetype, Filename] -> [Unsaved, Deleted, Empty] \t (Advanced)" },
            { "command": "sort_tabs_custom2",    "caption":"2: [Ext, LastActivated, Filetype, Filename] -> [Unsaved, Deleted, Empty] \t (Advanced)" },
            { "command": "sort_tabs_custom3",    "caption":"3: [Today, Directory, Filetype, Filename] -> [Unsaved, Deleted, Empty] \t (Advanced)" },
            // ExtRecentlyActivated

            // { "command": "sort_tabs_by_path_type_date",  "caption":"Sort Tabs by -> Custom"       },
            { "caption": "-" },
            // Simple Sorts
            { "command": "sort_tabs_by_file_path",       "caption":"Sort by -> Directory \t (Simple)" },
            { "command": "sort_tabs_by_name",            "caption":"Sort by -> File: Filename \t (Simple)" },
            { "command": "sort_tabs_by_syntax",          "caption":"Sort by -> File: Extension \t (Simple)" },
            { "command": "sort_tabs_by_date",            "caption":"Sort by -> File: Modified \t (Simple)" },
            { "command": "sort_tabs_by_syntax",          "caption":"Sort by -> Tab: Syntax \t (Simple)" },
            { "command": "sort_tabs_by_last_activation", "caption":"Sort by -> Tab: Activation \t (Simple)" },
            { "command": "sort_tabs_by_content_size",    "caption":"Sort by -> Tab: Content-Size \t (Simple)" },
            { "caption": "-" },
    ]},

    // ---------------------------------------------------------------------------------------------------------------------------
    {"caption": "-", "id": "0_separator"},
    {
        "command": "jorn_remove_duplicate_tabs",
        "caption": "Remove Duplicate Tabs \t ⴱ   ",
        //"caption": "─ Remove Duplicate Tabs \t ⴱ   ",
    },
    // ---------------------------------------------------------------------------------------------------------------------------
    {"caption": "-", "id": "0_separator"},
    {
        "command": "jorn_spread_files_in_current_layout",
        "caption": "Spread Files In Current Layout \t ☰   ",
        //"caption": "─ Spread Files In Current Layout \t ☰   ",
        "args": {}
    },
    {
        "command": "jorn_set_quad_layout",
        "caption": "Set Quad Layout \t ☰   ",
        //"caption": "─ Set Quad Layout \t ☰   ",
    },
    // {
    //     "command": "jorn_set_eight_by_eight_layout",
    //     "caption": "Set Eight Layout",
    ////     "caption": "─ Set Eight Layout",
    // },
    {
        "command": "jorn_set_dynamic_grid_layout",
        "caption": "Set Dynamic Grid Layout \t ☰   ",
        //"caption": "─ Set Dynamic Grid Layout \t ☰   ",
        // "args": {"rows": 3, "cols": 3}
        "args": {"rows": 2, "cols": 3}
    },
    {
        "command": "jorn_set_grid_for_open_files",
        "caption": "Fit Grid to Open Files \t ▦   ",
        //"caption": "─ Fit Grid to Open Files \t ▦   ",
        "args": {"resolution": 100, "build_full_grid": true},
    },
    {"caption": "-", "id": "0_separator"},


    // ---------------------------------------------------------------------------------------------------------------------------
    // Action-Items (commands available directly from the menu)
    // ---------------------------------------------------------------------------------------------------------------------------
    // (Common):
    {"caption": "-", "id": "0_separator"},
    {
        // -> Open Terminus Here (in a new tab)
        "command": "jorn_open_terminus_here",
        "caption": "Open Terminus Here \t  ▷   ",
        "args": {"group": -1, "index": -1 }
    },

    {"caption": "-", "id": "0_separator"},

    // ---------------------------------------------------------------------------------------------------------------------------
    // Submenus (menus within the menu)
    // ---------------------------------------------------------------------------------------------------------------------------

    // (Copy to clipboard):
    // -> Copy Filename: jorn_copy_file_name
    // -> Copy Basename: jorn_copy_base_name
    // -> Copy Extension: jorn_copy_file_extension
    // -> Copy Directory Path: jorn_copy_dir_path
    // -> Copy Full Path: jorn_copy_file_path
    {
        "id": "0_separator",
        "caption": "➔ Utils: Clipboard ... \t [...]  ",
        "children": [
            {"caption": "-", "id": "0_separator"},
            {
                "command": "jorn_copy_base_name",
                "caption": "Copy Basename       \t ▢",
                "args": {"group": -1, "index": -1 }
            },
            {
                "command": "jorn_copy_file_name",
                "caption": "Copy Filename       \t ▢",
                "args": {"group": -1, "index": -1 }
            },
            {
                "command": "jorn_copy_file_extension",
                "caption": "Copy Extension      \t ▢",
                "args": {"group": -1, "index": -1 }
            },
            {"caption": "-", "id": "0_separator"},
            {
                "command": "jorn_copy_dir_path",
                "caption": "Copy Location \t ▢",
                "args": {"group": -1, "index": -1 }
            },
            {
                "command": "jorn_copy_file_path",
                "caption": "Copy Path  \t ▢",
                "args": {"group": -1, "index": -1 }
            },

            {"caption": "-", "id": "0_separator"},
        ],
    },


    // {
    //     "id": "0_separator",
    //     "caption": "➔ Utils: Layout ... \t [...]  ",
    //     "children": [
    //         {
    //             "command": "jorn_set_quad_layout",
    //             "caption": "Set Quad Layout",
    //         },
    //         {"caption": "-", "id": "0_separator"},
    //     ]
    // },


    // (Open "Find in Files..." with options):
    // -> Find in Files: Current Tab's Directory: jorn_find_in_files_any_extension
    // -> Find in Files: Current Tab's Directory and Extension: jorn_find_in_files_current_extension
    {
        "id": "0_separator",
        "caption": "➔ Utils: Search ... \t [...]  ",
        "children": [
            {
                "command": "jorn_find_in_files_any_extension",
                "caption": "Search Directory \t ⌕",
                "args": {"group": -1, "index": -1 },
            },
            {"caption": "-", "id": "0_separator"},
            {
                "command": "jorn_find_in_files_current_extension",
                "caption": "Search Directory ( *.ext ) \t ⌕",
                "args": {"group": -1, "index": -1 },
            },

            {"caption": "-", "id": "0_separator"},
        ]
    },

    // ---------------------------------------------------------------------------------------------------------------------------


    // ---------------------------------------------------------------------------------------------------------------------------
    // [Settings-Toggle]
    // ---------------------------------------------------------------------------------------------------------------------------
   {"caption": "-", "id": "0_separator"},

    {
        // "caption"  : "[ Copy to Clipboard ] \t [...]  ",
        "caption": "⛭ Settings: Toggle \t [...]  ",
        "children": [


            {"caption": "-", "id": "0_separator"},
            {
                // Toggle Tab Scrolling On/Off
                "command": "jorn_toggle_tab_scrolling",
                "caption": "Toggle Tab Scrolling \t ⵙ",
            },
            {
                // Toggle Minimap On/Off
                "command": "jorn_toggle_minimap",
                "caption": "Toggle Minimap \t ⵙ",
            },
            {
                // Toggle Draw Whitespace On/Off
                "command": "jorn_toggle_draw_white_space",
                "caption": "Toggle Draw Whitespace \t ⵙ",
                "args": {"options": ["all", "none"] },
            },
        ],
    },

]
