# Import modules
import os
import sublime
import sublime_plugin
import time
import math


# class LimitHighlightingToMultipleCharacters(sublime_plugin.EventListener):
#     def on_selection_modified(self, view):
#         for region in view.sel():
#             if len(region) > 1:
#                 view.settings().set('match_selection', True)
#             else:
#                 view.settings().set('match_selection', False)


# class ShowMatchesCommand(sublime_plugin.TextCommand):
#     def run(self, edit):
#         # If there is a single empty selection, remove all marks
#         if len(self.view.sel()) == 1 and not self.view.sel()[0]:
#             return self.view.erase_regions('marked_sel')

#         matches = []
#         for sel in self.view.sel():
#             text = self.view.substr(sel)
#             # Skip matching if selection is only spaces
#             if not text or text.isspace():
#                 continue
#             found = self.view.find_all(text, sublime.LITERAL)
#             matches.extend(found)

#         self.view.add_regions('marked_sel', matches, 'region.redish')

# import functools
# class DoItAlways(sublime_plugin.ViewEventListener):
#     pending = 0

#     def on_selection_modified_async(self):
#         # Sanity check; only do this for smaller-ish files; may need tweaking.
#         if len(self.view) < 1048576:
#             self.pending += 1
#             sublime.set_timeout_async(functools.partial(self.update_find), 250)

#     def update_find(self):
#         self.pending -= 1
#         if self.pending != 0:
#             return

#         self.view.run_command("show_matches")


# [Open Terminus]
class JornOpenTerminusHereCommand(sublime_plugin.TextCommand):
    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        # Get file in tab (window > views[view/tab] > filepath).
        window = self.view.window()

        # This makes it possible to executa as command (in addition to tab context)
        if index == -1 or group == -1:
            active_view = window.active_view()
            index = window.get_view_index(active_view)[1]
            group = window.get_view_index(active_view)[0]

        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        file_path = view.file_name()

        # Specify startup options for the terminal
        cmd_title = ''
        cmd_folder = ''
        cmd_tag = ''
        if file_path is None:
            cmd_title = '(cmd.exe) dir:{}'.format('/Packages')
            cmd_folder = sublime.packages_path()
            cmd_tag = 'untitled'
        else:
            file_name = os.path.basename(file_path)
            dir_path = os.path.dirname(file_path)
            cmd_title = '(cmd.exe) {}'.format(file_name)
            cmd_folder = dir_path
            cmd_tag = file_path

        # Get the current layout
        layout = window.get_layout()
        num_cols = len(layout['cols']) - 1
        num_views = len(window.views())

        # If the tab doesn't have any views to its right, add a new view
        active_group = window.active_group()
        if active_group >= num_cols - 1:
            cols = [0.0]
            for i in range(1, num_cols + 1):
                cols.append(i / (num_cols + 1))
            cols.append(1.0)
            window.run_command("set_layout", {
                "cols": cols,
                "rows": [0.0, 1.0],
                "cells": [[i, 0, i + 1, 1] for i in range(num_cols + 1)]
            })

        window.focus_group(active_group + 1)
        window.run_command("terminus_open", {"title": cmd_title, "cwd": cmd_folder, "tag": cmd_tag})

        # Terminus -> README.md
        # window.run_command(
        #     "terminus_open", {
        #         "config_name": None,     # the shell config name, use `None` for the default config
        #         "cmd": None,             # the cmd to execute
        #         "shell_cmd": None,       # a script to execute in a shell
        #                                  # bash on Unix and cmd.exe on Windows
        #         "cwd": None,             # the working directory
        #         "working_dir": None,     # alias of "cwd"
        #         "env": {},               # extra environmental variables
        #         "title": None,           # title of the view, let terminal configures it if leave empty
        #         "panel_name": None,      # the name of the panel if terminal should be opened in panel
        #         "focus": True,           # focus to the panel
        #         "tag": None,             # a tag to identify the terminal
        #         "file_regex": None       # the `file_regex` pattern in sublime build system
        #                                  # see https://www.sublimetext.com/docs/3/build_systems.html
        #         "line_regex": None       # the `file_regex` pattern in sublime build system
        #         "pre_window_hooks": [],  # a list of window hooks before opening terminal
        #         "post_window_hooks": [], # a list of window hooks after opening terminal
        #         "post_view_hooks": [],   # a list of view hooks after opening terminal
        #         "auto_close": True,      # auto close terminal if process exits successfully
        #         "cancellable": False,    # allow `cancel_build` command to terminate process, only relevent to panels
        #         "timeit": False          # display elapsed time when the process terminates
        #     }
        # )

# [Open Current Folder]
class JornOpenCurrentFolderCommand(sublime_plugin.TextCommand):
    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        # Get file in tab (window > views[view/tab] > filepath).
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        file_path = view.file_name()
        # Exit if file wasn't found.
        if file_path is None:
            sublime.status_message('No File!')
            return None
        # Open folder containing file and select it
        file_name = os.path.basename(file_path)
        dir_path = os.path.dirname(file_path)
        sublime.status_message(file_path)
        window.run_command("open_dir", {"dir": dir_path, "file": file_name})

# [Search in Current Directory]
class JornFindInFilesAnyExtensionCommand(sublime_plugin.TextCommand):
    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        # Get file in tab (window > views[view/tab] > filepath).
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        file_path = view.file_name()
        # Exit if file wasn't found.
        if file_path is None:
            return None
        # Open 'Find in Files' and set 'Where' path.
        dir_path = os.path.dirname(file_path)
        selected_text = view.substr(view.sel()[0])
        window.run_command("show_panel", {"panel": "find_in_files", "where": (dir_path + ", *.*")})
        window.run_command("insert_text", {"characters": selected_text})
        sublime.status_message(dir_path)

# [Search in Current Directory (current extension)]
class JornFindInFilesCurrentExtensionCommand(sublime_plugin.TextCommand):
    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        # Get file in tab (window > views[view/tab] > filepath).
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        file_path = view.file_name()
        # Exit if file wasn't found.
        if file_path is None:
            return None
        # Open 'Find in Files' and set 'Where' path.
        dir_path = os.path.dirname(file_path)
        file_ext = os.path.splitext(file_path)[1]  # Get the file extension
        selected_text = view.substr(view.sel()[0])
        window.run_command("show_panel", {"panel": "find_in_files", "where": (dir_path + ", *" + file_ext)})
        window.run_command("insert_text", {"characters": selected_text})
        sublime.status_message(dir_path)

# [Copy Filename]
class JornCopyFileNameCommand(sublime_plugin.TextCommand):
    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        # Get file in tab (window > views[view/tab] > filepath).
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        file_path = view.file_name()
        # Exit if file wasn't found.
        if file_path is None:
            sublime.status_message('No File!')
            return None
        # Update clipboard and display message.
        file_name = os.path.basename(file_path)
        dir_path = os.path.dirname(file_path)
        sublime.set_clipboard(file_name)
        sublime.status_message(file_name)

# [Copy Basename]
class JornCopyBaseNameCommand(sublime_plugin.TextCommand):
    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        # Get file in tab (window > views[view/tab] > filepath).
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        file_path = view.file_name()
        # Exit if file wasn't found.
        if file_path is None:
            sublime.status_message('No File!')
            return None
        # Get basename without extension.
        file_name = os.path.basename(file_path)
        base_name = os.path.splitext(file_name)[0]
        # Update clipboard and display message.
        sublime.set_clipboard(base_name)
        sublime.status_message(base_name)

# [Copy Extension]
class JornCopyFileExtensionCommand(sublime_plugin.TextCommand):
    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        # Get file in tab (window > views[view/tab] > filepath).
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        file_path = view.file_name()
        # Exit if file wasn't found.
        if file_path is None:
            sublime.status_message('No File!')
            return None
        # Get the file extension.
        file_name, file_extension = os.path.splitext(file_path)
        # Update clipboard and display message.
        sublime.set_clipboard(file_extension)
        sublime.status_message(file_extension)

# [Copy Directory Path]
class JornCopyDirPathCommand(sublime_plugin.TextCommand):
    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        # Get file in tab (window > views[view/tab] > filepath).
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        file_path = view.file_name()
        # Exit if file wasn't found.
        if file_path is None:
            sublime.status_message('No File!')
            return None
        # Update clipboard and display message.
        file_name = os.path.basename(file_path)
        dir_path = os.path.dirname(file_path)
        sublime.set_clipboard(dir_path)
        sublime.status_message(dir_path)

# [Copy Complete Path]
class JornCopyFilePathCommand(sublime_plugin.TextCommand):
    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        # Get file in tab (window > views[view/tab] > filepath)
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        file_path = view.file_name()
        # Exit if tab didn't contain a saved file (lazy but functional errorhandling).
        if file_path is None:
            sublime.status_message('No File!')
            return None
        # Update clipboard and display message.
        sublime.set_clipboard(file_path)
        sublime.status_message(file_path)




# [Toggle Tab Scrolling]
class JornToggleTabScrollingCommand(sublime_plugin.TextCommand):
    def run(self, edit):
        settings = sublime.load_settings('Preferences.sublime-settings')
        current = settings.get('enable_tab_scrolling')
        sublime.status_message('enable_tab_scrolling: Changing from %s to %s' % (current, not current))
        settings.set('enable_tab_scrolling', not current)
        sublime.save_settings('Preferences.sublime-settings')

# [Toggle Minimap]
class JornToggleMinimapCommand(sublime_plugin.WindowCommand):
    def run(self):
        self.window.set_minimap_visible(not self.window.is_minimap_visible())


# [Toggle Draw Whitespace]
class JornToggleDrawWhiteSpaceCommand(sublime_plugin.TextCommand):
    def run(self, edit, options=["none", "selection", "all"]):
        try:
            current = self.view.settings().get("draw_white_space", "selection")
            index = options.index(current)
        except:
            index = 0

        index = (index + 1) % len(options)
        self.view.settings().set("draw_white_space", options[index])



# [Remove Duplicate Tabs]
class JornRemoveDuplicateTabsCommand(sublime_plugin.WindowCommand):
    def run(self):
        # Dictionary to hold the first view per file
        unique_views = {}
        # Iterate over all views in the window
        for view in self.window.views():
            # Get the file name associated with the view
            file_name = view.file_name()
            # If the file is not on disk, skip it
            if not file_name:
                continue
            # If we haven't encountered this file, remember the view
            if file_name not in unique_views:
                unique_views[file_name] = view
            else:
                # If we have a duplicate, close the view
                # Ensure the view isn't dirty before closing
                if not view.is_dirty():
                    self.window.focus_view(view)
                    self.window.run_command('close_file')
        # Focus back to the first view of the last unique file
        if unique_views:
            self.window.focus_view(next(reversed(unique_views.values())))

# [Set Quad Layout]
class JornSetQuadLayoutCommand(sublime_plugin.WindowCommand):
    def run(self):
        # Set the layout to grid (quad view)
        layout = {
            "cols": [0.0, 0.5, 1.0],
            "rows": [0.0, 0.5, 1.0],
            "cells": [
                [0, 0, 1, 1], [1, 0, 2, 1],
                [0, 1, 1, 2], [1, 1, 2, 2]
            ]
        }
        self.window.set_layout(layout)
        # Distribute open files across the panes
        views = self.window.views()
        num_views = len(views)
        for i, view in enumerate(views):
            group = i % 4  # There are 4 panes in the quad layout
            self.window.set_view_index(view, group, 0)
        # Focus on the first file in the first pane
        if views:
            self.window.focus_view(views[0])

# [Spread Tabs in Layout]
class JornSpreadFilesInCurrentLayoutCommand(sublime_plugin.WindowCommand):
    def run(self):
        views = self.window.views()
        layout = self.window.get_layout()
        num_panes = len(layout['cells'])
        for i, view in enumerate(views):
            group = i % num_panes
            self.window.set_view_index(view, group, 0)
        if views:
            self.window.focus_view(views[0])




# [Set Grid Layout]
class JornSetDynamicGridLayoutCommand(sublime_plugin.WindowCommand):
    def run(self, rows=3, cols=2):
        # Validate input
        if rows <= 0 or cols <= 0:
            sublime.error_message("Invalid grid size. Rows and columns must be greater than 0.")
            return
        # Generate columns and rows for the layout
        cols_list = [i / cols for i in range(cols + 1)]
        rows_list = [i / rows for i in range(rows + 1)]
        # Generate cells for the layout
        cells = [[col, row, col + 1, row + 1] for col in range(cols) for row in range(rows)]

        print(cells)
        # result: [[0, 0, 1, 1], [0, 1, 1, 2], [1, 0, 2, 1], [1, 1, 2, 2], [2, 0, 3, 1], [2, 1, 3, 2]]
        # result: [[0, 0, 1, 1], [0, 1, 1, 2], [1, 0, 2, 1], [1, 1, 2, 2], [2, 0, 3, 1], [2, 1, 3, 2], [3, 0, 4, 1], [3, 1, 4, 2]]
        # result: [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2], [0, 2, 1, 3], [1, 2, 2, 3], [0, 3, 1, 4], [1, 3, 2, 4]]
        # result: [[0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1], [3, 0, 4, 1], [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2], [3, 1, 4, 2]]

        # 3 columns 2 rows
        # [[0, 0, 1, 1], [0, 1, 1, 2], [1, 0, 2, 1], [1, 1, 2, 2], [2, 0, 3, 1], [2, 1, 3, 2]]



        # Set the layout
        layout = {
            "cols": cols_list,
            "rows": rows_list,
            "cells": cells
        }
        self.window.set_layout(layout)
        # Distribute open files across the panes
        views = self.window.views()
        num_panes = rows * cols
        for i, view in enumerate(views):
            group = i % num_panes
            self.window.set_view_index(view, group, 0)
        # Focus on the first file in the first pane
        if views:
            self.window.focus_view(views[0])


import math
class JornSetGridForOpenFilesCommand(sublime_plugin.WindowCommand):
    def run(self, resolution=100, build_full_grid=True):
        views = self.window.views()
        num_views = len(views)
        print("Number of open files: {0}".format(num_views))
        if num_views == 0:
            sublime.error_message("No open files to arrange.")
            return
        # Adjust the number of views based on the resolution
        adjusted_num_views = math.ceil(num_views * (resolution / 100))
        print("Adjusted number of views (after applying resolution): {0}".format(adjusted_num_views))
        # Calculate the number of rows and columns
        cols = int(math.ceil(math.sqrt(adjusted_num_views)))
        rows = int(math.ceil(adjusted_num_views / float(cols)))
        # Adjust rows and columns to be even if required
        if build_full_grid:
            if rows % 2 != 0:
                rows += 1
            if cols % 2 != 0 and cols * (rows - 1) >= adjusted_num_views:
                cols -= 1
        print("Grid dimensions (rows x cols): {0}x{1}".format(rows, cols))
        # Generate columns and rows for the layout
        cols_list = [i / float(cols) for i in range(cols + 1)]
        rows_list = [i / float(rows) for i in range(rows + 1)]
        # Generate cells for the layout
        cells = [[col, row, col + 1, row + 1] for row in range(rows) for col in range(cols)]
        print("Generated cells: {0}".format(cells))
        # Set the layout
        layout = {
            "cols": cols_list,
            "rows": rows_list,
            "cells": cells
        }
        self.window.set_layout(layout)
        # Distribute open files across the panes
        for i, view in enumerate(views):
            group = i % adjusted_num_views
            self.window.set_view_index(view, group, 0)
        # Focus on the first file in the first pane
        if views:
            self.window.focus_view(views[0])



# =======================================================

import os
import re
import sublime
import sublime_plugin
import hashlib
class JornIncrementalSaveCommand(sublime_plugin.WindowCommand):
    def run(self):
        view = self.window.active_view()
        if not view or not view.file_name():
            sublime.error_message("No file is currently open.")
            return

        original_path = view.file_name()
        new_path = self.increment_filename(original_path)

        # Check if the new filename already exists
        if os.path.exists(new_path):
            # Check if the existing file is a duplicate
            if self.is_duplicate(original_path, new_path):
                sublime.status_message(f"Duplicate detected. No new file created: {new_path}")
                return
            else:
                # Find a unique filename
                new_path = self.find_unique_non_duplicate_filename(original_path)

        try:
            # Read the content of the original file
            with open(original_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Write content to the new file
            with open(new_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Open the new file in Sublime Text
            self.window.open_file(new_path)
            sublime.status_message(f"Created: {new_path}")
        except Exception as e:
            sublime.error_message(f"Error creating file: {e}")

    def is_duplicate(self, file_path1, file_path2):
        """
        Compare two files to check if they are duplicates.
        """
        size1 = os.path.getsize(file_path1)
        size2 = os.path.getsize(file_path2)

        if size1 != size2:
            return False

        # For small files, compare hashes
        if size1 < 10 * 1024 * 1024:
            hash1 = self.calculate_hash(file_path1)
            hash2 = self.calculate_hash(file_path2)
            return hash1 == hash2
        else:
            # For large files, compare contents chunk by chunk
            return self.compare_files(file_path1, file_path2)

    def calculate_hash(self, file_path, chunk_size=65536):
        """
        Calculate MD5 hash of a file in chunks to handle large files.
        """
        hasher = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(chunk_size), b""):
                hasher.update(chunk)
        return hasher.hexdigest()

    def compare_files(self, file_path1, file_path2, chunk_size=65536):
        """
        Compare two large files by reading chunks.
        """
        with open(file_path1, "rb") as f1, open(file_path2, "rb") as f2:
            while True:
                chunk1 = f1.read(chunk_size)
                chunk2 = f2.read(chunk_size)
                if chunk1 != chunk2:
                    return False
                if not chunk1:
                    break
        return True

    def increment_filename(self, filepath, separator="_"):
        """
        Increments the filename in the given filepath based on specific rules,
        appending a numeric suffix if the incremented name already exists.
        """
        dir_path, filename = os.path.split(filepath)
        name, ext = os.path.splitext(filename)
        segments = name.split(separator)

        # Copy of segments to use when appending suffix
        base_segments = segments.copy()

        # Attempt to increment the last applicable segment
        incremented = False
        for i in range(len(segments)-1, -1, -1):
            segment = segments[i]
            if re.fullmatch(r'\d+', segment):
                segments[i] = str(int(segment) + 1).zfill(len(segment))
                incremented = True
                break
            elif re.fullmatch(r'[a-z]', segment):
                segments[i] = 'a' if segment == 'z' else chr(ord(segment) + 1)
                incremented = True
                break
            elif re.fullmatch(r'[A-Z]', segment):
                segments[i] = 'A' if segment == 'Z' else chr(ord(segment) + 1)
                incremented = True
                break

        if not incremented:
            segments.append('1')

        new_name = separator.join(segments) + ext
        new_path = os.path.join(dir_path, new_name) if dir_path else new_name

        return new_path

    def find_unique_non_duplicate_filename(self, original_path, separator="_"):
        """
        Continues to increment the filename until a non-duplicate, unique filename is found.
        """
        while True:
            new_path = self.increment_filename(original_path, separator)
            if not os.path.exists(new_path):
                return new_path
            elif not self.is_duplicate(original_path, new_path):
                return new_path
            else:
                # Update the original path to the new path for further incrementation
                original_path = new_path

    def test_increment_filename(self):
        test_cases = {
            '.../v4_1_code.py': '.../v4_2_code.py',
            '.../v4_01_code.py': '.../v4_02_code.py',
            '.../v4_1_a_prompt.md': '.../v4_1_b_prompt.md',
            '.../v5_1_f.py': '.../v5_1_g.py',
            '.../v5_1_f_1.py': '.../v5_1_f_2.py',
            '.../v5_1_f_01.py': '.../v5_1_f_02.py',
            '.../file_version_aa.txt': '.../file_version_aa_1.txt',
            '.../file_version_aa_a.txt': '.../file_version_aa_b.txt',
            '.../file_version_aa_A.txt': '.../file_version_aa_B.txt',
            '.../file_version_aa_B_01.txt': '.../file_version_aa_B_02.txt',
            '.../TabUtils_2_a.py': '.../TabUtils_2_b.py',
            '.../TabUtils_2_b.py': '.../TabUtils_2_b_1.py',
            '.../TabUtils_2_a_1.py': '.../TabUtils_2_a_2.py',
            '.../Jorn_TabUtils_2.py': '.../Jorn_TabUtils_3.py',
            '.../Jorn_TabUtils_2_a.py': '.../Jorn_TabUtils_2_b.py',
            '.../Jorn_TabUtils_2_b.py': '.../Jorn_TabUtils_2_b_1.py',
            '.../v1_01_c_prompt_1_b.md': '.../v1_01_c_prompt_1_c.md',
            '.../v1_01_c_prompt_1_c.md': '.../v1_01_c_prompt_1_d.md',
            '.../v1_01_c_prompt_1_d.md': '.../v1_01_c_prompt_1_e.md',
        }

        for input_path, expected_output in test_cases.items():
            result = self.increment_filename(input_path)
            assert result == expected_output, f"Test failed for input: {input_path}, expected: {expected_output}, got: {result}"

        print("All tests passed.")


# =======================================================

